'use strict';

var keys = require('../');
var test = require('tape');
var runTests = require('./tests');

test('as a function', function (t) {
	t.test('bad array/this value', function (st) {
		st['throws'](function () { keys(undefined); }, TypeError, 'undefined is not an object');
		st['throws'](function () { keys(null); }, TypeError, 'null is not an object');
		st.end();
	});

	runTests(keys, t);

	t.end();
});
