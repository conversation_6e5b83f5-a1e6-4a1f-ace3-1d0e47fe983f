{"name": "request-promise-core", "version": "1.1.3", "description": "Core Promise support implementation for the simplified HTTP request client 'request'.", "keywords": ["xhr", "http", "https", "promise", "request", "then", "thenable", "core"], "main": "./lib/plumbing.js", "scripts": {"test": "./node_modules/.bin/gulp ci", "test-publish": "./node_modules/.bin/gulp ci-no-cov", "publish-please": "publish-please", "prepublish": "publish-please guard"}, "repository": {"type": "git", "url": "git+https://github.com/request/promise-core.git"}, "author": "<PERSON><PERSON> (https://github.com/analog-nico)", "license": "ISC", "bugs": {"url": "https://github.com/request/promise-core/issues"}, "homepage": "https://github.com/request/promise-core#readme", "engines": {"node": ">=0.10.0"}, "dependencies": {"lodash": "^4.17.15"}, "peerDependencies": {"request": "^2.34"}, "devDependencies": {"@request/api": "^0.6.0", "@request/client": "^0.1.0", "bluebird": "~3.4.1", "body-parser": "~1.15.2", "chai": "~3.5.0", "chalk": "~1.1.3", "gulp": "~3.9.1", "gulp-coveralls": "~0.1.4", "gulp-eslint": "~2.1.0", "gulp-istanbul": "~1.0.0", "gulp-mocha": "~2.2.0", "node-version": "~1.0.0", "publish-please": "~2.4.1", "request": "^2.34.0", "rimraf": "~2.5.3", "run-sequence": "~1.2.2", "stealthy-require": "~1.0.0"}}