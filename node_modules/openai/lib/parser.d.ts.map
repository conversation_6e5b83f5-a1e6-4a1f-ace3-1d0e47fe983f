{"version": 3, "file": "parser.d.ts", "sourceRoot": "", "sources": ["../src/lib/parser.ts"], "names": [], "mappings": "OACO,EACL,cAAc,EACd,0BAA0B,EAC1B,8BAA8B,EAC9B,0BAA0B,EAC1B,qBAAqB,EACrB,qCAAqC,EACrC,uCAAuC,EACvC,0BAA0B,EAC1B,8BAA8B,EAC9B,oBAAoB,EAGrB;OACM,EAAE,KAAK,kCAAkC,EAAE;OAC3C,EAAE,wBAAwB,EAAE;AAEnC,KAAK,6BAA6B,GAC9B,0BAA0B,GAC1B,8BAA8B,CAAC,GAAG,CAAC,GACnC,uCAAuC,CAAC,GAAG,CAAC,GAC5C,0BAA0B,CAAC;AAE/B,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAEjD,KAAK,QAAQ,GAAG,QAAQ,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC,CAAC;AAElE,wBAAgB,4BAA4B,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI,IAAI,0BAA0B,CAE/F;AAED,MAAM,MAAM,8BAA8B,CAAC,MAAM,SAAS,6BAA6B,IACrF,MAAM,CAAC,iBAAiB,CAAC,SAAS,2BAA2B,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAEpF,MAAM,MAAM,2BAA2B,CAAC,OAAO,IAAI,wBAAwB,GAAG;IAC5E,QAAQ,EAAE,OAAO,CAAC;IAElB,MAAM,EAAE,gCAAgC,CAAC;IACzC,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC;CACrC,CAAC;AAEF,wBAAgB,2BAA2B,CAAC,OAAO,EACjD,eAAe,EAAE,wBAAwB,EACzC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,OAAO,GACnC,2BAA2B,CAAC,OAAO,CAAC,CAetC;AAED,MAAM,MAAM,uBAAuB,CAAC,OAAO,IAAI,kCAAkC,GAAG;IAClF,QAAQ,EAAE,OAAO,CAAC;IAElB,MAAM,EAAE,gCAAgC,CAAC;IACzC,SAAS,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC;CACrC,CAAC;AAEF,wBAAgB,uBAAuB,CAAC,OAAO,EAC7C,eAAe,EAAE,kCAAkC,EACnD,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,OAAO,GACnC,uBAAuB,CAAC,OAAO,CAAC,CAelC;AAED,wBAAgB,4BAA4B,CAAC,OAAO,EAClD,eAAe,EAAE,GAAG,GACnB,eAAe,IAAI,2BAA2B,CAAC,OAAO,CAAC,CAEzD;AAED,KAAK,WAAW,GAAG;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,GAAG,CAAC;IACf,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC;CAC7C,CAAC;AAEF,MAAM,MAAM,iBAAiB,CAC3B,QAAQ,SAAS,WAAW,EAC5B,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,QAAQ,GAAG,IAAI,GAAG,KAAK,IAChE,0BAA0B,GAAG;IAC/B,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;IACnC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IACzB,aAAa,EAAE,WAAW,CAAC;IAE3B,MAAM,EAAE,qBAAqB,CAAC;IAC9B,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC;IAC9D,SAAS,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;CAChD,CAAC;AAEF,wBAAgB,iBAAiB,CAAC,QAAQ,SAAS,WAAW,EAC5D,IAAI,EAAE,0BAA0B,EAChC,EACE,MAAM,EACN,QAAQ,GACT,EAAE;IACD,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,QAAQ,CAAC,WAAW,CAAC,CAAC;IACnD,QAAQ,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC;CAC5C,GACA,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAmB1C;AAED,wBAAgB,kBAAkB,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,IAAI,iBAAiB,CAAC,GAAG,CAAC,CAE5E;AAED,wBAAgB,wBAAwB,CACtC,MAAM,SAAS,0BAA0B,GAAG,IAAI,EAChD,OAAO,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,8BAA8B,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAC1F,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAwB3E;AAED,wBAAgB,mBAAmB,CACjC,MAAM,SAAS,0BAA0B,EACzC,OAAO,GAAG,8BAA8B,CAAC,MAAM,CAAC,EAChD,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,GAAG,oBAAoB,CAAC,OAAO,CAAC,CA+B3E;AA2CD,wBAAgB,mBAAmB,CACjC,MAAM,EAAE,0BAA0B,GAAG,IAAI,GAAG,SAAS,EACrD,QAAQ,EAAE,qCAAqC,GAC9C,OAAO,CAaT;AAED,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,6BAA6B,GAAG,OAAO,CAUpF;AAED,wBAAgB,iDAAiD,CAC/D,SAAS,EAAE,qBAAqB,CAAC,YAAY,CAAC,GAC7C,OAAO,CAAC,SAAS,IAAI,qCAAqC,EAAE,CAQ9D;AAED,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,8BAA8B,CAAC,OAAO,CAAC,QAchF"}