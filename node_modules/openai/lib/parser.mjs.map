{"version": 3, "file": "parser.mjs", "sourceRoot": "", "sources": ["../src/lib/parser.ts"], "names": [], "mappings": "OAAO,EAAE,8BAA8B,EAAE,uBAAuB,EAAE,WAAW,EAAE;AA4B/E,MAAM,UAAU,4BAA4B,CAAC,IAAc;IACzD,OAAO,IAAI,KAAK,SAAS,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC;AACjF,CAAC;AAYD,MAAM,UAAU,2BAA2B,CACzC,eAAyC,EACzC,MAAoC;IAEpC,MAAM,GAAG,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;IAEnC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;QAC3B,MAAM,EAAE;YACN,KAAK,EAAE,gCAAgC;YACvC,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SAClB;KACF,CAAC,CAAC;IAEH,OAAO,GAA2C,CAAC;AACrD,CAAC;AASD,MAAM,UAAU,uBAAuB,CACrC,eAAmD,EACnD,MAAoC;IAEpC,MAAM,GAAG,GAAG,EAAE,GAAG,eAAe,EAAE,CAAC;IAEnC,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;QAC3B,MAAM,EAAE;YACN,KAAK,EAAE,gCAAgC;YACvC,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SAClB;KACF,CAAC,CAAC;IAEH,OAAO,GAAuC,CAAC;AACjD,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,eAAoB;IAEpB,OAAO,eAAe,EAAE,CAAC,QAAQ,CAAC,KAAK,gCAAgC,CAAC;AAC1E,CAAC;AAqBD,MAAM,UAAU,iBAAiB,CAC/B,IAAgC,EAChC,EACE,MAAM,EACN,QAAQ,GAIT;IAED,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IAExB,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE;QAC3B,MAAM,EAAE;YACN,KAAK,EAAE,qBAAqB;YAC5B,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,MAAM;YACb,UAAU,EAAE,KAAK;SAClB;QACD,SAAS,EAAE;YACT,KAAK,EAAE,QAAQ;YACf,UAAU,EAAE,KAAK;SAClB;KACF,CAAC,CAAC;IAEH,OAAO,GAA+C,CAAC;AACzD,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,IAAS;IAC1C,OAAO,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,qBAAqB,CAAC;AACpD,CAAC;AAED,MAAM,UAAU,wBAAwB,CAGtC,UAA0B,EAAE,MAAc;IAC1C,IAAI,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;QAC9C,OAAO;YACL,GAAG,UAAU;YACb,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACzC,iDAAiD,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBAE7E,OAAO;oBACL,GAAG,MAAM;oBACT,OAAO,EAAE;wBACP,GAAG,MAAM,CAAC,OAAO;wBACjB,MAAM,EAAE,IAAI;wBACZ,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;4BAC7B;gCACE,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU;6BACtC;4BACH,CAAC,CAAC,SAAS,CAAC;qBACb;iBACF,CAAC;YACJ,CAAC,CAAC;SAC8B,CAAC;IACrC,CAAC;IAED,OAAO,mBAAmB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACjD,CAAC;AAED,MAAM,UAAU,mBAAmB,CAGjC,UAA0B,EAAE,MAAc;IAC1C,MAAM,OAAO,GAAiC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAyB,EAAE;QACrG,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;YACtC,MAAM,IAAI,uBAAuB,EAAE,CAAC;QACtC,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,KAAK,gBAAgB,EAAE,CAAC;YAC9C,MAAM,IAAI,8BAA8B,EAAE,CAAC;QAC7C,CAAC;QAED,iDAAiD,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE7E,OAAO;YACL,GAAG,MAAM;YACT,OAAO,EAAE;gBACP,GAAG,MAAM,CAAC,OAAO;gBACjB,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC7B;wBACE,UAAU,EACR,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAI,SAAS;qBAC7F;oBACH,CAAC,CAAC,SAAS,CAAC;gBACZ,MAAM,EACJ,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACjD,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;oBACrD,CAAC,CAAC,IAAI;aACT;SACuB,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,OAAO,EAAE,GAAG,UAAU,EAAE,OAAO,EAAE,CAAC;AACpC,CAAC;AAED,SAAS,mBAAmB,CAG1B,MAAc,EAAE,OAAe;IAC/B,IAAI,MAAM,CAAC,eAAe,EAAE,IAAI,KAAK,aAAa,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,MAAM,CAAC,eAAe,EAAE,IAAI,KAAK,aAAa,EAAE,CAAC;QACnD,IAAI,WAAW,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC1C,MAAM,eAAe,GAAG,MAAM,CAAC,eAAuD,CAAC;YAEvF,OAAO,eAAe,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CACpB,MAAc,EACd,QAA+C;IAE/C,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAClC,CAAC,SAAS,EAAE,EAAE,CACZ,4BAA4B,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,IAAI,CACvD,CAAC,CAAC,kDAAkD;IAC/F,OAAO;QACL,GAAG,QAAQ;QACX,QAAQ,EAAE;YACR,GAAG,QAAQ,CAAC,QAAQ;YACpB,gBAAgB,EACd,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAChF,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACtE,CAAC,CAAC,IAAI;SACT;KACF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,mBAAmB,CACjC,MAAqD,EACrD,QAA+C;IAE/C,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACrD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAClC,CAAC,SAAS,EAAE,EAAE,CACZ,4BAA4B,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,QAAQ,EAAE,IAAI,KAAK,QAAQ,CAAC,QAAQ,CAAC,IAAI,CACjG,CAAC;IACF,OAAO,CACL,4BAA4B,CAAC,SAAS,CAAC;QACvC,CAAC,kBAAkB,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE,QAAQ,CAAC,MAAM,IAAI,KAAK,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,MAAqC;IACzE,IAAI,4BAA4B,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;QACzD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,MAAM,CAAC,KAAK,EAAE,IAAI,CAChB,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,CACtF,IAAI,KAAK,CACX,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,iDAAiD,CAC/D,SAA8C;IAE9C,KAAK,MAAM,QAAQ,IAAI,SAAS,IAAI,EAAE,EAAE,CAAC;QACvC,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACjC,MAAM,IAAI,WAAW,CACnB,oEAAoE,QAAQ,CAAC,IAAI,IAAI,CACtF,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,KAA8C;IAC/E,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC;QAC/B,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,MAAM,IAAI,WAAW,CACnB,2EAA2E,IAAI,CAAC,IAAI,IAAI,CACzF,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YAClC,MAAM,IAAI,WAAW,CACnB,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,4FAA4F,CACxH,CAAC;QACJ,CAAC;IACH,CAAC;AACH,CAAC"}