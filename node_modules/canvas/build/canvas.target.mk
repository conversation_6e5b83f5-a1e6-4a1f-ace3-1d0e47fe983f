# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := canvas
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=canvas' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DHAVE_GIF' \
	'-DHAVE_JPEG' \
	'-DHAVE_RSVG' \
	'-DNAPI_DISABLE_CPP_EXCEPTIONS' \
	'-DNODE_ADDON_API_ENABLE_MAYBE' \
	'-DBUILDING_NODE_EXTENSION' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-g \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-std=gnu++17

INCS_Debug := \
	-I/github/home/<USER>/node-gyp/21.7.3/include/node \
	-I/github/home/<USER>/node-gyp/21.7.3/src \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/openssl/config \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/openssl/openssl/include \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/uv/include \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/zlib \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/v8/include \
	-I$(srcdir)/node_modules/node-addon-api \
	-I/usr/local/include/cairo \
	-I/usr/local/include \
	-I/usr/local/include/glib-2.0 \
	-I/usr/local/lib/glib-2.0/include \
	-I/usr/local/include/pixman-1 \
	-I/usr/local/include/freetype2 \
	-I/usr/local/include/libpng16 \
	-I/usr/local/include/pango-1.0 \
	-I/usr/local/include/fribidi \
	-I/usr/local/include/harfbuzz \
	-I/usr/local/include/librsvg-2.0 \
	-I/usr/local/include/gdk-pixbuf-2.0

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=canvas' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DHAVE_GIF' \
	'-DHAVE_JPEG' \
	'-DHAVE_RSVG' \
	'-DNAPI_DISABLE_CPP_EXCEPTIONS' \
	'-DNODE_ADDON_API_ENABLE_MAYBE' \
	'-DBUILDING_NODE_EXTENSION'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-std=gnu++17

INCS_Release := \
	-I/github/home/<USER>/node-gyp/21.7.3/include/node \
	-I/github/home/<USER>/node-gyp/21.7.3/src \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/openssl/config \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/openssl/openssl/include \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/uv/include \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/zlib \
	-I/github/home/<USER>/node-gyp/21.7.3/deps/v8/include \
	-I$(srcdir)/node_modules/node-addon-api \
	-I/usr/local/include/cairo \
	-I/usr/local/include \
	-I/usr/local/include/glib-2.0 \
	-I/usr/local/lib/glib-2.0/include \
	-I/usr/local/include/pixman-1 \
	-I/usr/local/include/freetype2 \
	-I/usr/local/include/libpng16 \
	-I/usr/local/include/pango-1.0 \
	-I/usr/local/include/fribidi \
	-I/usr/local/include/harfbuzz \
	-I/usr/local/include/librsvg-2.0 \
	-I/usr/local/include/gdk-pixbuf-2.0

OBJS := \
	$(obj).target/$(TARGET)/src/backend/Backend.o \
	$(obj).target/$(TARGET)/src/backend/ImageBackend.o \
	$(obj).target/$(TARGET)/src/backend/PdfBackend.o \
	$(obj).target/$(TARGET)/src/backend/SvgBackend.o \
	$(obj).target/$(TARGET)/src/bmp/BMPParser.o \
	$(obj).target/$(TARGET)/src/Backends.o \
	$(obj).target/$(TARGET)/src/Canvas.o \
	$(obj).target/$(TARGET)/src/CanvasGradient.o \
	$(obj).target/$(TARGET)/src/CanvasPattern.o \
	$(obj).target/$(TARGET)/src/CanvasRenderingContext2d.o \
	$(obj).target/$(TARGET)/src/closure.o \
	$(obj).target/$(TARGET)/src/color.o \
	$(obj).target/$(TARGET)/src/Image.o \
	$(obj).target/$(TARGET)/src/ImageData.o \
	$(obj).target/$(TARGET)/src/init.o \
	$(obj).target/$(TARGET)/src/register_font.o \
	$(obj).target/$(TARGET)/src/FontParser.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.cc FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-Wl,-rpath '-Wl,$$ORIGIN' \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-Wl,-rpath '-Wl,$$ORIGIN' \
	-m64

LIBS := \
	-L/usr/local/lib \
	-lpixman-1 \
	-lcairo \
	-lpng16 \
	-lz \
	-lpangocairo-1.0 \
	-lpango-1.0 \
	-lgobject-2.0 \
	-lglib-2.0 \
	-lharfbuzz \
	-lfreetype \
	-lrsvg-2 \
	-lm \
	-lgio-2.0 \
	-lgdk_pixbuf-2.0 \
	-ljpeg \
	-lgif

$(obj).target/canvas.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/canvas.node: LIBS := $(LIBS)
$(obj).target/canvas.node: TOOLSET := $(TOOLSET)
$(obj).target/canvas.node: $(OBJS) FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(obj).target/canvas.node
# Add target alias
.PHONY: canvas
canvas: $(builddir)/canvas.node

# Copy this to the executable output path.
$(builddir)/canvas.node: TOOLSET := $(TOOLSET)
$(builddir)/canvas.node: $(obj).target/canvas.node FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/canvas.node
# Short alias for building this executable.
.PHONY: canvas.node
canvas.node: $(obj).target/canvas.node $(builddir)/canvas.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/canvas.node

