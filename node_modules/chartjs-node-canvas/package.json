{"name": "chartjs-node-canvas", "version": "5.0.0", "description": "A node renderer for Chart.js using canvas.", "main": "./dist/index", "type": "commonjs", "types": "./dist/index.d.ts", "scripts": {"nvm": "cat .nvmrc | nvm use", "build": "tsc", "clean": "clean-dest -s ./src -d ./dist --file-map ./scripts/clean-dest --permanent --verbose && echo TODO: Delete .tsbuildinfo?", "lint": "echo TODO: Add linting", "test": "c8 --all mocha dist/**/*.spec.js", "test-unit": "mocha --exclude dist/**/*.e2e.spec.js dist/**/*.spec.js", "test-e2e": "mocha dist/**/*.e2e.spec.js", "package-size": "node ./scripts/package-size", "debug-test": "node --inspect-brk=33295 --nolazy node_modules/mocha/bin/_mocha dist/**/*.spec.js", "watch-build": "tsc --watch", "watch-clean": "nodemon --watch ./src -e ts --exec npm run-script clean", "watch-test": "nodemon --watch ./dist -e js --exec npm run-script test", "docs": "jsdoc2md dist/index.js > API.md"}, "bugs": {"url": "https://github.com/SeanSobey/ChartjsNodeCanvas/issues", "email": "<EMAIL>"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/SeanSobey/ChartjsNodeCanvas", "repository": {"type": "git", "url": "https://github.com/SeanSobey/ChartjsNodeCanvas.git"}, "readme": "README.md", "license": "MIT", "dependencies": {"canvas": "^3.1.0", "tslib": "^2.8.1"}, "peerDependencies": {"chart.js": "^4.4.8"}, "devDependencies": {"@types/mocha": "^7.0.2", "@types/node": "^16.10.4", "@types/offscreencanvas": "^2019.7.3", "c8": "^7.10.0", "chart.js": "^4.4.8", "chartjs-plugin-annotation": "^3.1.0", "chartjs-plugin-crosshair": "^2.0.0", "chartjs-plugin-datalabels": "^2.2.0", "clean-dest": "^1.3.3", "gifencoder": "^2.0.1", "jsdoc-to-markdown": "^5.0.3", "mocha": "^7.2.0", "nodemon": "^2.0.13", "release-it": "^14.11.6", "resemblejs": "^4.0.0", "source-map-support": "^0.5.20", "ts-std-lib": "^1.2.2", "typescript": "^5.7.3", "wtfnode": "^0.9.1"}}