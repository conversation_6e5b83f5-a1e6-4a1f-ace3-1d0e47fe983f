import { ChartConfiguration } from 'chart.js/auto';
import { ChartJSNodeCanvasBase, MimeType } from './chartJSNodeCanvasBase';
export declare class AnimatedChartJSNodeCanvas extends ChartJSNodeCanvasBase {
    /**
     * Render to a data url array.
     * @see https://github.com/Automattic/node-canvas#canvastodataurl
     *
     * @param configuration The Chart JS configuration for the chart to render.
     * @param mimeType A string indicating the image format. Valid options are `image/png`, `image/jpeg` (if node-canvas was built with JPEG support), `raw` (unencoded ARGB32 data in native-endian byte order, top-to-bottom), `application/pdf` (for PDF canvases) and image/svg+xml (for SVG canvases). Defaults to `image/png` for image canvases, or the corresponding type for PDF or SVG canvas.
     */
    renderToDataURL(configuration: ChartConfiguration, mimeType?: MimeType): Promise<ReadonlyArray<string>>;
    /**
     * Render to a buffer.
     * @see https://github.com/Automattic/node-canvas#canvastobuffer
     *
     * @param configuration The Chart JS configuration for the chart to render.
     * @param mimeType A string indicating the image format. Valid options are `image/png`, `image/jpeg` (if node-canvas was built with JPEG support) or `raw` (unencoded ARGB32 data in native-endian byte order, top-to-bottom). Defaults to `image/png` for image canvases, or the corresponding type for PDF or SVG canvas.
     */
    renderToBuffer(configuration: ChartConfiguration, mimeType?: MimeType): Promise<ReadonlyArray<Buffer>>;
    private renderChart;
}
