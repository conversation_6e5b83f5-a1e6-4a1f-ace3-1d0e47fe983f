{"version": 3, "file": "chartJSNodeCanvasBase.js", "sourceRoot": "", "sources": ["../src/chartJSNodeCanvasBase.ts"], "names": [], "mappings": ";;;AAIA,+BAAwC;AACxC,iDAA8C;AAC9C,qEAAkE;AA4DlE,MAAsB,qBAAqB;IAU1C;;;;OAIG;IACH,YAAY,OAAiC;QAE5C,IAAI,OAAO,KAAK,IAAI,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC7D,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAChD,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAA,2BAAY,EAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,EAAgB,CAAC;QACtE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;;;;;;OAOG;IACI,YAAY,CAAC,IAAY,EAAE,OAAuF;QAExH,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACnC,CAAC;IAES,UAAU,CAAC,OAAiC;;QAErD,MAAM,OAAO,GAAmB,OAAO,CAAC,eAAe,CAAC,CAAC;QAEzD,IAAI,MAAA,OAAO,CAAC,OAAO,0CAAE,oBAAoB,EAAE,CAAC;YAC3C,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;gBAC3D,OAAO,CAAC,MAAM,CAAC,CAAC;gBAChB,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YAC/C,CAAC;QACF,CAAC;QAED,IAAI,MAAA,OAAO,CAAC,OAAO,0CAAE,oBAAoB,EAAE,CAAC;YAC1C,MAAc,CAAC,KAAK,GAAG,OAAO,CAAC;YAChC,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;gBAC3D,IAAA,2BAAY,EAAC,MAAM,CAAC,CAAC;YACtB,CAAC;YACD,OAAQ,MAAc,CAAC,KAAK,CAAC;QAC9B,CAAC;QAED,IAAI,MAAA,OAAO,CAAC,OAAO,0CAAE,MAAM,EAAE,CAAC;YAC7B,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC7C,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;oBAChC,OAAO,CAAC,QAAQ,CAAC,IAAA,2BAAY,EAAC,MAAM,CAAC,CAAC,CAAC;gBACxC,CAAC;qBAAM,CAAC;oBACP,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAC1B,CAAC;YACF,CAAC;QACF,CAAC;QAED,IAAI,MAAA,OAAO,CAAC,OAAO,0CAAE,aAAa,EAAE,CAAC;YACpC,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;gBACpD,OAAO,CAAC,QAAQ,CAAC,IAAA,2BAAY,EAAC,MAAM,CAAC,CAAC,CAAC;YACxC,CAAC;QACF,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC3B,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC9B,OAAO,CAAC,QAAQ,CAAC,IAAI,+CAAsB,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;QACvG,CAAC;QACD,MAAM,WAAW,GAAI,IAAA,WAAQ,EAAC,cAAc,EAAC,UAAU,CAAC,CAAC;QAEzD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9C,IAAI,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/B,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC3B,CAAC;QACF,CAAC;QAED,OAAO,OAAO,CAAC;IAChB,CAAC;CACD;AAtGD,sDAsGC"}