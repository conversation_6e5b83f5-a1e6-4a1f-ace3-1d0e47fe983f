{"version": 3, "file": "example.js", "sourceRoot": "", "sources": ["../src/example.ts"], "names": [], "mappings": ";;;AAAA,yBAAsD;AAGtD,wDAAwB;AACxB,2BAAoC;AAEpC,KAAK,UAAU,IAAI;IAElB,MAAM,KAAK,GAAG,GAAG,CAAC;IAClB,MAAM,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,aAAa,GAAuB;QACzC,IAAI,EAAE,KAAK;QACX,IAAI,EAAE;YACL,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;YAC9D,QAAQ,EAAE,CAAC;oBACV,KAAK,EAAE,YAAY;oBACnB,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAC1B,eAAe,EAAE;wBAChB,yBAAyB;wBACzB,yBAAyB;wBACzB,yBAAyB;wBACzB,yBAAyB;wBACzB,0BAA0B;wBAC1B,yBAAyB;qBACzB;oBACD,WAAW,EAAE;wBACZ,oBAAoB;wBACpB,uBAAuB;wBACvB,uBAAuB;wBACvB,uBAAuB;wBACvB,wBAAwB;wBACxB,uBAAuB;qBACvB;oBACD,WAAW,EAAE,CAAC;iBACd,CAAC;SACF;QACD,OAAO,EAAE,EACR;QACD,OAAO,EAAE,CAAC;gBACT,EAAE,EAAE,mBAAmB;gBACvB,UAAU,EAAE,CAAC,KAAK,EAAE,EAAE;oBACrB,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;oBACtB,GAAG,CAAC,IAAI,EAAE,CAAC;oBACX,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC;oBACxB,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;oBAClC,GAAG,CAAC,OAAO,EAAE,CAAC;gBACf,CAAC;aACD,CAAC;KACF,CAAC;IACF,MAAM,aAAa,GAAkB,CAAC,OAAO,EAAE,EAAE;QAChD,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;QACnC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,GAAG,KAAK,CAAC;IAC9C,CAAC,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAEpB,MAAM,iBAAiB,GAAG,IAAI,oBAAiB,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;IAClF,MAAM,MAAM,GAAG,MAAM,iBAAiB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IACrE,MAAM,aAAE,CAAC,SAAS,CAAC,yBAAyB,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IAEhE,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,cAAc,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACrG,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAEvB,qGAAqG;IACrG,iFAAiF;IACjF,8CAA8C;IAC9C,yCAAyC;IACzC,8BAA8B;IAC9B,mDAAmD;IACnD,2FAA2F;IAC3F,0EAA0E;IAC1E,+BAA+B;IAC/B,oCAAoC;IACpC,wDAAwD;AACzD,CAAC;AACD,IAAI,EAAE,CAAC"}