{"version": 3, "file": "chartJSNodeCanvas.js", "sourceRoot": "", "sources": ["../src/chartJSNodeCanvas.ts"], "names": [], "mappings": ";;;AAGA,mEAAkF;AAElF,MAAa,iBAAkB,SAAQ,6CAAqB;IAE3D;;;;;;OAMG;IACI,eAAe,CAAC,aAAiC,EAAE,WAAqB,WAAW;QAEzF,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC9C,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC5C,CAAC;YACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;YACtC,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAmB,EAAE,GAAW,EAAE,EAAE;gBAC/D,KAAK,CAAC,OAAO,EAAE,CAAC;gBAChB,IAAI,KAAK,EAAE,CAAC;oBACX,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;gBACtB,CAAC;gBACD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,mBAAmB,CAAC,aAAiC,EAAE,WAAqB,WAAW;QAE7F,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACnC,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC3C,4DAA4D;QAC5D,gJAAgJ;QAChJ,mEAAmE;QACnE,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,OAAO,OAAO,CAAC;IAChB,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,aAAiC,EAAE,WAAqB,WAAW;QAExF,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC9C,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACnC,CAAC;YACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;YACtC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAmB,EAAE,MAAc,EAAE,EAAE;gBACvD,KAAK,CAAC,OAAO,EAAE,CAAC;gBAChB,IAAI,KAAK,EAAE,CAAC;oBACX,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;gBACtB,CAAC;gBACD,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC,EAAE,QAAQ,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB,CAAC,aAAiC,EAAE,WAA2D,WAAW;QAElI,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACnC,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzC,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,OAAO,MAAM,CAAC;IACf,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CAAC,aAAiC,EAAE,WAAyC,WAAW;QAE5G,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACnC,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAgB,CAAC;QACtC,YAAY,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACpC,QAAQ,QAAQ,EAAE,CAAC;YAClB,KAAK,WAAW;gBACf,OAAO,MAAM,CAAC,eAAe,EAAE,CAAC;YACjC,KAAK,YAAY;gBAChB,OAAO,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAClC,KAAK,iBAAiB;gBACrB,OAAO,MAAM,CAAC,eAAe,EAAE,CAAC;YACjC;gBACC,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;QACtD,CAAC;IACF,CAAC;IAEO,WAAW,CAAC,aAAiC;QAEpD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACxE,MAAc,CAAC,KAAK,GAAI,MAAc,CAAC,KAAK,IAAI,EAAE,CAAC;QACpD,aAAa,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,IAAI,EAAE,CAAC;QACpD,aAAa,CAAC,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;QACzC,6DAA6D;QAC7D,aAAa,CAAC,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QACxC,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvC,MAAc,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,4BAA4B;QACjE,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAE,OAAe,EAAE,aAAa,CAAC,CAAC;QACjE,OAAQ,MAAc,CAAC,KAAK,CAAC;QAC7B,OAAO,KAAK,CAAC;IACd,CAAC;CACD;AAtID,8CAsIC"}