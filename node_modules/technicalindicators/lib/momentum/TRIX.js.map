{"version": 3, "file": "TRIX.js", "sourceRoot": "", "sources": ["../../src/momentum/TRIX.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,YAAY,CAAA;AAEZ,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,EAAE,GAAG,EAAE,MAAM,2BAA2B,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAEnE,MAAM,gBAAiB,SAAQ,cAAc;CAG5C;AAAA,CAAC;AAEF,MAAM,WAAY,SAAQ,SAAS;IAGjC,YAAY,KAAe;QACzB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,UAAU,GAAI,KAAK,CAAC,MAAM,CAAC;QAC/B,IAAI,MAAM,GAAQ,KAAK,CAAC,MAAM,CAAC;QAC/B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzB,IAAI,GAAG,GAAgB,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,MAAM,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAC5F,IAAI,QAAQ,GAAW,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,MAAM,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAC5F,IAAI,aAAa,GAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,MAAM,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAC5F,IAAI,OAAO,GAAY,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,CAAC,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QAEvF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE,CAAC;gBACZ,IAAI,UAAU,GAAa,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,cAAc,GAAS,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACnF,IAAI,oBAAoB,GAAG,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAChG,IAAI,MAAM,GAAiB,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBACtG,IAAI,GAAG,MAAM,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAA,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAIC,SAAS,CAAC,KAAY;QACpB,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,EAAE,CAAA,CAAC,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC;YAChC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;IAC5B,CAAC;IAAA,CAAC;;AANK,cAAS,GAAC,IAAI,CAAC;AAS1B,MAAM,eAAe,KAAe;IAChC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}