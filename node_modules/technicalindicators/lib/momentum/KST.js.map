{"version": 3, "file": "KST.js", "sourceRoot": "", "sources": ["../../src/momentum/KST.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAC;AAC7C,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAE5B,MAAM,eAAgB,SAAQ,cAAc;CAW3C;AAED,MAAM;CAGL;AAED,MAAM,UAAW,SAAQ,SAAS;IAGhC,YAAY,KAAc;QACxB,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,UAAU,GAAI,KAAK,CAAC,MAAM,CAAC;QAC/B,IAAI,OAAO,GAAO,KAAK,CAAC,OAAO,CAAC;QAChC,IAAI,OAAO,GAAO,KAAK,CAAC,OAAO,CAAC;QAChC,IAAI,OAAO,GAAO,KAAK,CAAC,OAAO,CAAC;QAChC,IAAI,OAAO,GAAO,KAAK,CAAC,OAAO,CAAC;QAEhC,IAAI,OAAO,GAAO,KAAK,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,GAAO,KAAK,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,GAAO,KAAK,CAAC,UAAU,CAAC;QACnC,IAAI,OAAO,GAAO,KAAK,CAAC,UAAU,CAAC;QAEnC,IAAI,YAAY,GAAE,KAAK,CAAC,YAAY,CAAC;QAErC,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC,CAAC;QAC3D,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC,CAAC;QAC3D,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC,CAAC;QAC3D,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAC,CAAC,CAAC;QAE3D,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QACvF,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QACvF,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QACvF,IAAI,IAAI,GAAU,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAC;QACvF,IAAI,SAAS,GAAK,IAAI,GAAG,CAAC,EAAE,MAAM,EAAG,YAAY,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAG,CAAC,CAAC,EAAE,EAAE,GAAE,MAAM,CAAC,CAAC,CAAA,CAAA,CAAC,EAAC,CAAC,CAAA;QAC3F,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QAEjB,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,GAAC,OAAO,EAAE,OAAO,GAAC,OAAO,EAAE,OAAO,GAAC,OAAO,CAAC,CAAC;QACjG,IAAI,CAAC,SAAS,GAAG,CAAC,QAAQ,CAAC;YACzB,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,GAAG,CAAC;YACR,IAAI,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,KAAK,EAAC,MAAM,EAAC,MAAM,CAAC;YAC1C,OAAO,IAAI,EAAE,CAAC;gBACZ,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,KAAK,GAAG,CAAC,UAAU,KAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC1E,KAAK,GAAG,CAAC,UAAU,KAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC1E,KAAK,GAAG,CAAC,UAAU,KAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC1E,KAAK,GAAG,CAAC,UAAU,KAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC1E,EAAE,CAAA,CAAC,KAAK,GAAG,WAAW,CAAC,CAAA,CAAC;oBACtB,KAAK,EAAE,CAAC;gBACV,CAAC;gBAAA,IAAI,CAAC,CAAC;oBACL,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;gBAC7D,CAAC;gBACD,MAAM,GAAG,CAAC,GAAG,KAAG,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAClE,MAAM,GAAG,GAAG,KAAG,SAAS,CAAC,CAAC,CAAC;oBACzB,GAAG,EAAG,MAAM,CAAC,GAAG,CAAC;oBACjB,MAAM,EAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;iBAC7C,CAAC,CAAC,CAAC,SAAS,CAAC;gBACd,IAAI,GAAG,MAAM,MAAM,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QAEtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,EAAE,CAAA,CAAC,MAAM,CAAC,KAAK,IAAI,SAAS,CAAC,CAAA,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAAA,CAAC;IAKF,SAAS,CAAE,KAAY;QACrB,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,EAAE,CAAA,CAAC,UAAU,CAAC,KAAK,IAAI,SAAS,CAAC;YAC/B,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;IAC5B,CAAC;IAAA,CAAC;;AAPK,aAAS,GAAG,GAAG,CAAC;AAUzB,MAAM,cAAc,KAAc;IAC3B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,EAAE,CAAA,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrB,CAAC;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,MAAM,CAAC,MAAM,CAAC;AAClB,CAAC;AAAA,CAAC"}