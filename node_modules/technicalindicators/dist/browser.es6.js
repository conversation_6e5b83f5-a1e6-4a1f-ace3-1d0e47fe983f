(function(e){'use strict';function r(e){return ee[e]}function t(e){let t=r('precision');return t?parseFloat(e.toPrecision(t)):e}function l(e){te.reverseInputs(e);var r=new le(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function o(e){te.reverseInputs(e);var r=new oe(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function a(e){te.reverseInputs(e);var r=new ae(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function n(e){te.reverseInputs(e);var r=new ne(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function s(e){te.reverseInputs(e);var r=new se(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function u(e){te.reverseInputs(e);var r=new ue(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function i(e){te.reverseInputs(e);var r=new ie(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function h(e){te.reverseInputs(e);var r=new he(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function p(e){te.reverseInputs(e);var r=new pe(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function g(e){te.reverseInputs(e);var r=new ge(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function v(e){te.reverseInputs(e);var r=new me(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function d(e){te.reverseInputs(e);var r=new we(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function c(e){te.reverseInputs(e);var r=new fe(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function m(e){te.reverseInputs(e);var r=new Ie(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function x(e){te.reverseInputs(e);var r=new _e(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function w(e){te.reverseInputs(e);var r=new Pe(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function f(e){te.reverseInputs(e);var r=new Ce(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function I(e){te.reverseInputs(e);var r=new be(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function _(e){te.reverseInputs(e);var r=new qe(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function P(e){te.reverseInputs(e);var r=new Ee(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function C(e){te.reverseInputs(e);var r=new Se(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function b(e){te.reverseInputs(e);var r=new Ve(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function q(e){te.reverseInputs(e);var r=new Ae(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function E(e){te.reverseInputs(e);var r=new ke(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function S(e){te.reverseInputs(e);var r=new Be(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function V(e,r,t,l){return e<=t&&r>=t||t<=e&&l>=e}function A(e){te.reverseInputs(e);var r=new He(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function k(e){te.reverseInputs(e);var r=new Le(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function B(e){te.reverseInputs(e);var r=new Te(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function H(e){te.reverseInputs(e);var r=new Me(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function z(e){te.reverseInputs(e);var r=new De(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function L(e){te.reverseInputs(e);var r=new Re(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function T(e){te.reverseInputs(e);var r=new je(e).result;return e.reversedInput&&(r.open.reverse(),r.high.reverse(),r.low.reverse(),r.close.reverse(),r.volume.reverse(),r.timestamp.reverse()),te.reverseInputs(e),r}function M(e){te.reverseInputs(e);var r=new Oe(e).result;return e.reversedInput&&(r.open.reverse(),r.high.reverse(),r.low.reverse(),r.close.reverse(),r.volume.reverse(),r.timestamp.reverse()),te.reverseInputs(e),r}function D(e){return new Ze().hasPattern(e)}function R(e){return new $e().hasPattern(e)}function j(e){return new er().hasPattern(e)}function O(e){return new rr().hasPattern(e)}function y(e){te.reverseInputs(e);var r=new Sr(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function F(e){te.reverseInputs(e);var r=new Vr(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function U(e){te.reverseInputs(e);var r=new Ar(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function W(e){te.reverseInputs(e);var r=new kr(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function K(e){te.reverseInputs(e);var r=new Br(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}function G(){return['sma','ema','wma','wema','macd','rsi','bollingerbands','adx','atr','truerange','roc','kst','psar','stochastic','williamsr','adl','obv','trix','cci','awesomeoscillator','forceindex','vwap','volumeprofile','renko','heikinashi','stochasticrsi','mfi','averagegain','averageloss','highest','lowest','sum','FixedSizeLinkedList','sd','bullish','bearish','abandonedbaby','doji','bearishengulfingpattern','bullishengulfingpattern','darkcloudcover','downsidetasukigap','dragonflydoji','gravestonedoji','bullishharami','bearishharami','bullishharamicross','bearishharamicross','eveningdojistar','eveningstar','morningdojistar','morningstar','bullishmarubozu','bearishmarubozu','piercingline','bullishspinningtop','bearishspinningtop','threeblackcrows','threewhitesoldiers','bullishhammerstick','bearishhammerstick','bullishinvertedhammerstick','bearishinvertedhammerstick','hammerpattern','hammerpatternunconfirmed','hangingman','hangingmanunconfirmed','shootingstar','shootingstarunconfirmed','tweezertop','tweezerbottom','ichimokucloud','keltnerchannels','chandelierexit','crossup','crossdown','crossover']}var X=Math.min,N=Math.max,J=Math.abs;class Q{constructor(e,r,t){this.next=t,t&&(t.prev=this),this.prev=r,r&&(r.next=this),this.data=e}}class Y{constructor(){this._length=0}get head(){return this._head&&this._head.data}get tail(){return this._tail&&this._tail.data}get current(){return this._current&&this._current.data}get length(){return this._length}push(e){this._tail=new Q(e,this._tail),0===this._length&&(this._head=this._tail,this._current=this._head,this._next=this._head),this._length++}pop(){var e=this._tail;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,e.data):(this._tail=e.prev,this._tail.next=void 0,this._current===e&&(this._current=this._tail,this._next=void 0),e.data)}shift(){var e=this._head;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,e.data):(this._head=this._head.next,this._current===e&&(this._current=this._head,this._next=this._current.next),e.data)}unshift(e){this._head=new Q(e,void 0,this._head),0===this._length&&(this._tail=this._head,this._next=this._head),this._length++}unshiftCurrent(){var e=this._current;return e===this._head||2>this._length?e&&e.data:(e===this._tail?(this._tail=e.prev,this._tail.next=void 0,this._current=this._tail):(e.next.prev=e.prev,e.prev.next=e.next,this._current=e.prev),this._next=this._current.next,e.next=this._head,e.prev=void 0,this._head.prev=e,this._head=e,e.data)}removeCurrent(){var e=this._current;if(0!==this._length)return(this._length--,0===this._length)?(this._head=this._tail=this._current=this._next=void 0,e.data):(e===this._tail?(this._tail=e.prev,this._tail.next=void 0,this._current=this._tail):e===this._head?(this._head=e.next,this._head.prev=void 0,this._current=this._head):(e.next.prev=e.prev,e.prev.next=e.next,this._current=e.prev),this._next=this._current.next,e.data)}resetCursor(){return this._current=this._next=this._head,this}next(){var e=this._next;if(void 0!==e)return this._next=e.next,this._current=e,e.data}}class Z extends Y{constructor(e,r,t,l){if(super(),this.size=e,this.maintainHigh=r,this.maintainLow=t,this.maintainSum=l,this.totalPushed=0,this.periodHigh=0,this.periodLow=Infinity,this.periodSum=0,!e||'number'!=typeof e)throw'Size required and should be a number.';this._push=this.push,this.push=function(e){this.add(e),this.totalPushed++}}add(e){this.length===this.size?(this.lastShift=this.shift(),this._push(e),this.maintainHigh&&this.lastShift==this.periodHigh&&this.calculatePeriodHigh(),this.maintainLow&&this.lastShift==this.periodLow&&this.calculatePeriodLow(),this.maintainSum&&(this.periodSum-=this.lastShift)):this._push(e),this.maintainHigh&&this.periodHigh<=e&&(this.periodHigh=e),this.maintainLow&&this.periodLow>=e&&(this.periodLow=e),this.maintainSum&&(this.periodSum+=e)}*iterator(){for(this.resetCursor();this.next();)yield this.current}calculatePeriodHigh(){for(this.resetCursor(),this.next()&&(this.periodHigh=this.current);this.next();)this.periodHigh<=this.current&&(this.periodHigh=this.current)}calculatePeriodLow(){for(this.resetCursor(),this.next()&&(this.periodLow=this.current);this.next();)this.periodLow>=this.current&&(this.periodLow=this.current)}}class ${constructor(){this.open=[],this.high=[],this.low=[],this.close=[],this.volume=[],this.timestamp=[]}}let ee={};class re{}class te{constructor(e){this.format=e.format||t}static reverseInputs(e){e.reversedInput&&(e.values?e.values.reverse():void 0,e.open?e.open.reverse():void 0,e.high?e.high.reverse():void 0,e.low?e.low.reverse():void 0,e.close?e.close.reverse():void 0,e.volume?e.volume.reverse():void 0,e.timestamp?e.timestamp.reverse():void 0)}getResult(){return this.result}}class le extends te{constructor(e){super(e),this.period=e.period,this.price=e.values;this.generator=function*(e){var r,t=new Y,l=0,o=1,a=yield;for(t.push(0);;)o<e?(o++,t.push(a),l+=a):(l=l-t.shift()+a,r=l/e,t.push(a)),a=yield r}(this.period),this.generator.next(),this.result=[],this.price.forEach((e)=>{var r=this.generator.next(e);r.value!==void 0&&this.result.push(this.format(r.value))})}nextValue(e){var r=this.generator.next(e).value;if(r!=void 0)return this.format(r)}}le.calculate=l;class oe extends te{constructor(e){super(e);var r,t=e.period,l=e.values;this.result=[],r=new le({period:t,values:[]});this.generator=function*(){for(var e,l=yield;;)void 0!=e&&void 0!==l?(e=(l-e)*(2/(t+1))+e,l=yield e):(l=yield,e=r.nextValue(l),e&&(l=yield e))}(),this.generator.next(),this.generator.next(),l.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(this.format(r.value))})}nextValue(e){var r=this.generator.next(e).value;if(r!=void 0)return this.format(r)}}oe.calculate=o;class ae extends te{constructor(e){super(e);var r=e.period,t=e.values;this.result=[],this.generator=function*(){for(let t=new Y;;)if(t.length<r)t.push((yield));else{t.resetCursor();let l=0;for(let e=1;e<=r;e++)l+=t.next()*e/(r*(r+1)/2);var e=yield l;t.shift(),t.push(e)}}(),this.generator.next(),t.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(this.format(r.value))})}nextValue(e){var r=this.generator.next(e).value;if(r!=void 0)return this.format(r)}}ae.calculate=a;class ne extends te{constructor(e){super(e);var r,t=e.period,l=e.values;this.result=[],r=new le({period:t,values:[]});this.generator=function*(){for(var e,l=yield;;)void 0!=e&&void 0!==l?(e=(l-e)*(1/t)+e,l=yield e):(l=yield,e=r.nextValue(l),void 0!==e&&(l=yield e))}(),this.generator.next(),this.generator.next(),l.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(this.format(r.value))})}nextValue(e){var r=this.generator.next(e).value;if(r!=void 0)return this.format(r)}}ne.calculate=n;class se extends te{constructor(e){super(e);var r=e.SimpleMAOscillator?le:oe,t=e.SimpleMASignal?le:oe,l=new r({period:e.fastPeriod,values:[],format:(e)=>e}),o=new r({period:e.slowPeriod,values:[],format:(e)=>e}),a=new t({period:e.signalPeriod,values:[],format:(e)=>e}),n=this.format;this.result=[],this.generator=function*(){for(var r,t,s,u,i,h,p=0;;){if(p<e.slowPeriod){r=yield,i=l.nextValue(r),h=o.nextValue(r),p++;continue}i&&h&&(t=i-h,s=a.nextValue(t)),u=t-s,r=yield{MACD:n(t),signal:s?n(s):void 0,histogram:isNaN(u)?void 0:n(u)},i=l.nextValue(r),h=o.nextValue(r)}}(),this.generator.next(),e.values.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(r.value)})}nextValue(e){var r=this.generator.next(e).value;return r}}se.calculate=s;class ue extends te{constructor(e){super(e);let r=e.values,t=e.period,l=this.format;this.generator=function*(e){var r,t,o=yield,a=1,n=0,s=o;for(o=yield;;)t=o-s,t=0<t?t:0,0<t&&(n+=t),a<e?a++:void 0==r?r=n/e:r=(r*(e-1)+t)/e,s=o,r=void 0===r?void 0:l(r),o=yield r}(t),this.generator.next(),this.result=[],r.forEach((e)=>{var r=this.generator.next(e);r.value!==void 0&&this.result.push(r.value)})}nextValue(e){return this.generator.next(e).value}}ue.calculate=u;class ie extends te{constructor(e){super(e);let r=e.values,t=e.period,l=this.format;this.generator=function*(e){var r,t,o=yield,a=1,n=0,s=o;for(o=yield;;)t=s-o,t=0<t?t:0,0<t&&(n+=t),a<e?a++:void 0==r?r=n/e:r=(r*(e-1)+t)/e,s=o,r=void 0===r?void 0:l(r),o=yield r}(t),this.generator.next(),this.result=[],r.forEach((e)=>{var r=this.generator.next(e);r.value!==void 0&&this.result.push(r.value)})}nextValue(e){return this.generator.next(e).value}}ie.calculate=i;class he extends te{constructor(e){super(e);var r=e.period,t=e.values,l=new ue({period:r,values:[]}),o=new ie({period:r,values:[]});let a=1;this.generator=function*(){for(var e,r,t,n,s=yield;;)e=l.nextValue(s),r=o.nextValue(s),void 0!==e&&void 0!==r&&(0===r?n=100:0===e?n=0:(t=e/r,t=isNaN(t)?0:t,n=parseFloat((100-100/(1+t)).toFixed(2)))),a++,s=yield n}(r),this.generator.next(),this.result=[],t.forEach((e)=>{var r=this.generator.next(e);r.value!==void 0&&this.result.push(r.value)})}nextValue(e){return this.generator.next(e).value}}he.calculate=h;class pe extends te{constructor(e){super(e);var r=e.period,t=e.values,l=new le({period:r,values:[],format:(e)=>e});this.result=[],this.generator=function*(){var e,t,o=new Z(r);e=yield;for(var a;;){if(o.push(e),t=l.nextValue(e),t){let e=0;for(let r of o.iterator())e+=Math.pow(r-t,2);a=Math.sqrt(e/r)}e=yield a}}(),this.generator.next(),t.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(this.format(r.value))})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return this.format(r.value)}}pe.calculate=p;class ge extends te{constructor(e){super(e);var r,t,l=e.period,o=e.values,a=e.stdDev,n=this.format;this.result=[],r=new le({period:l,values:[],format:(e)=>e}),t=new pe({period:l,values:[],format:(e)=>e}),this.generator=function*(){var e,l,o,s;for(l=yield;;){if(o=r.nextValue(l),s=t.nextValue(l),o){let r=n(o),t=n(o+s*a),u=n(o-s*a),i=n((l-u)/(t-u));e={middle:r,upper:t,lower:u,pb:i}}l=yield e}}(),this.generator.next(),o.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(r.value)})}nextValue(e){return this.generator.next(e).value}}ge.calculate=g;class ve extends te{constructor(e){super(e),this.period=e.period,this.price=e.values;this.generator=function*(e){for(var r=new Y,t=0,l=1,o=yield,a=0;;)l<e?(l++,t+=o,a=void 0):l==e?(l++,t+=o,a=t):a=a-a/e+o,o=yield a}(this.period),this.generator.next(),this.result=[],this.price.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(this.format(r.value))})}nextValue(e){var r=this.generator.next(e).value;if(r!=void 0)return this.format(r)}}ve.calculate=function(e){te.reverseInputs(e);var r=new ve(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r};class de extends te{constructor(e){super(e);var r=e.low,t=e.high,l=this.format;if(r.length!=t.length)throw'Inputs(low,high) not of equal size';this.result=[],this.generator=function*(){for(var e,r,t=yield;;){if(r){let o=t.high-r.high,a=r.low-t.low;e=l(a>o&&0<a?a:0)}r=t,t=yield e}}(),this.generator.next(),r.forEach((e,l)=>{var o=this.generator.next({high:t[l],low:r[l]});o.value!==void 0&&this.result.push(o.value)})}static calculate(e){te.reverseInputs(e);var r=new de(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}nextValue(e){return this.generator.next(e).value}}class ce extends te{constructor(e){super(e);var r=e.low,t=e.high,l=this.format;if(r.length!=t.length)throw'Inputs(low,high) not of equal size';this.result=[],this.generator=function*(){for(var e,r,t=yield;;){if(r){let o=t.high-r.high,a=r.low-t.low;e=l(o>a&&0<o?o:0)}r=t,t=yield e}}(),this.generator.next(),r.forEach((e,l)=>{var o=this.generator.next({high:t[l],low:r[l]});o.value!==void 0&&this.result.push(o.value)})}static calculate(e){te.reverseInputs(e);var r=new ce(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r}nextValue(e){return this.generator.next(e).value}}class me extends te{constructor(e){super(e);var r=e.low,t=e.high,l=e.close,o=this.format;if(r.length!=t.length)throw'Inputs(low,high) not of equal size';this.result=[],this.generator=function*(){for(var e,r,t=yield;;)void 0==e&&(e=t.close,t=yield r),r=N(t.high-t.low,isNaN(J(t.high-e))?0:J(t.high-e),isNaN(J(t.low-e))?0:J(t.low-e)),e=t.close,void 0!=r&&(r=o(r)),t=yield r}(),this.generator.next(),r.forEach((e,o)=>{var a=this.generator.next({high:t[o],low:r[o],close:l[o]});a.value!=void 0&&this.result.push(a.value)})}nextValue(e){return this.generator.next(e).value}}me.calculate=v;class xe extends re{}class we extends te{constructor(e){super(e);var r=e.low,t=e.high,l=e.close,o=e.period,a=this.format,n=new ce({high:[],low:[]}),s=new de({high:[],low:[]}),u=new ve({period:o,values:[],format:(e)=>e}),i=new ve({period:o,values:[],format:(e)=>e}),h=new ve({period:o,values:[],format:(e)=>e}),p=new ne({period:o,values:[],format:(e)=>e}),g=new me({low:[],high:[],close:[]});if(r.length!==t.length||t.length!==l.length)throw'Inputs(low,high, close) not of equal size';this.result=[],xe,this.generator=function*(){var e,r,t,l,o,a,v,d=yield;for(e=0,r=0,t=0;;){let e=g.nextValue(d),r=n.nextValue(d),t=s.nextValue(d);if(e===void 0){d=yield;continue}let c=h.nextValue(e),m=u.nextValue(r),x=i.nextValue(t);if(c!=void 0&&m!=void 0&&x!=void 0){l=100*m/c,o=100*x/c;let e=J(l-o),r=l+o;a=100*(e/r),v=p.nextValue(a)}d=yield{adx:v,pdi:l,mdi:o}}}(),this.generator.next(),r.forEach((e,o)=>{var n=this.generator.next({high:t[o],low:r[o],close:l[o]});n.value!=void 0&&n.value.adx!=void 0&&this.result.push({adx:a(n.value.adx),pdi:a(n.value.pdi),mdi:a(n.value.mdi)})})}nextValue(e){let r=this.generator.next(e).value;if(r!=void 0&&r.adx!=void 0)return{adx:this.format(r.adx),pdi:this.format(r.pdi),mdi:this.format(r.mdi)}}}we.calculate=d;class fe extends te{constructor(e){super(e);var r=e.low,t=e.high,l=e.close,o=e.period,a=this.format;if(r.length!==t.length||t.length!==l.length)throw'Inputs(low,high, close) not of equal size';var n=new me({low:[],high:[],close:[]}),s=new ne({period:o,values:[],format:(e)=>e});this.result=[],this.generator=function*(){for(var e,r,t=yield;;)r=n.nextValue({low:t.low,high:t.high,close:t.close}),e=void 0===r?void 0:s.nextValue(r),t=yield e}(),this.generator.next(),r.forEach((e,o)=>{var n=this.generator.next({high:t[o],low:r[o],close:l[o]});n.value!==void 0&&this.result.push(a(n.value))})}nextValue(e){return this.generator.next(e).value}}fe.calculate=c;class Ie extends te{constructor(e){super(e);var r=e.period,t=e.values;this.result=[],this.generator=function*(){let e=1;for(var t,l=new Z(r),o=yield;;)l.push(o),e<r?e++:t=100*((o-l.lastShift)/l.lastShift),o=yield t}(),this.generator.next(),t.forEach((e)=>{var r=this.generator.next(e);r.value==void 0||isNaN(r.value)||this.result.push(this.format(r.value))})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0&&!isNaN(r.value))return this.format(r.value)}}Ie.calculate=m;class _e extends te{constructor(e){super(e);let r=e.values,t=e.ROCPer1,l=e.ROCPer2,o=e.ROCPer3,a=e.ROCPer4,n=e.SMAROCPer1,s=e.SMAROCPer2,u=e.SMAROCPer3,i=e.SMAROCPer4,h=e.signalPeriod,p=new Ie({period:t,values:[]}),g=new Ie({period:l,values:[]}),v=new Ie({period:o,values:[]}),d=new Ie({period:a,values:[]}),c=new le({period:n,values:[],format:(e)=>e}),m=new le({period:s,values:[],format:(e)=>e}),x=new le({period:u,values:[],format:(e)=>e}),w=new le({period:i,values:[],format:(e)=>e}),f=new le({period:h,values:[],format:(e)=>e});var I=this.format;this.result=[];let _=N(t+n,l+s,o+u,a+i);this.generator=function*(){for(let e,r,t,l,o,a,n,s=1,u=yield;;){let i=p.nextValue(u),h=g.nextValue(u),P=v.nextValue(u),C=d.nextValue(u);r=i===void 0?void 0:c.nextValue(i),t=h===void 0?void 0:m.nextValue(h),l=P===void 0?void 0:x.nextValue(P),o=C===void 0?void 0:w.nextValue(C),s<_?s++:e=1*r+2*t+3*l+4*o,a=e===void 0?void 0:f.nextValue(e),n=e===void 0?void 0:{kst:I(e),signal:a?I(a):void 0},u=yield n}}(),this.generator.next(),r.forEach((e)=>{let r=this.generator.next(e);r.value!=void 0&&this.result.push(r.value)})}nextValue(e){let r=this.generator.next(e);if(r.value!=void 0)return r.value}}_e.calculate=x;class Pe extends te{constructor(e){super(e);let r=e.high||[],t=e.low||[];this.result=[],this.generator=function*(e,r){for(let t,l,o,a,n=!0,s=e,u=yield;;)t?(o+=s*(l-o),n?(o=X(o,a.low,u.low),t.high>l&&(l=t.high,s=X(s+e,r))):(o=N(o,a.high,u.high),t.low<l&&(l=t.low,s=X(s+e,r))),(n&&t.low<o||!n&&t.high>o)&&(s=e,o=l,n=!n,l=n?t.high:t.low)):(o=u.low,l=u.high),a=u,t&&(u=t),t=yield o}(e.step,e.max),this.generator.next(),t.forEach((e,l)=>{var o=this.generator.next({high:r[l],low:t[l]});o.value!==void 0&&this.result.push(o.value)})}nextValue(e){let r=this.generator.next(e);if(r.value!==void 0)return r.value}}Pe.calculate=w;class Ce extends te{constructor(e){super(e);let r=e.low,t=e.high,l=e.close,o=e.period,a=e.signalPeriod,n=this.format;if(r.length!==t.length||t.length!==l.length)throw'Inputs(low,high, close) not of equal size';this.result=[],this.generator=function*(){let e,r,t=1,l=new Z(o,!0,!1),s=new Z(o,!1,!0),u=new le({period:a,values:[],format:(e)=>e});for(var i=yield;;){if(l.push(i.high),s.push(i.low),t<o){t++,i=yield;continue}let a=s.periodLow;e=100*((i.close-a)/(l.periodHigh-a)),e=isNaN(e)?0:e,r=u.nextValue(e),i=yield{k:n(e),d:void 0===r?void 0:n(r)}}}(),this.generator.next(),r.forEach((e,o)=>{var a=this.generator.next({high:t[o],low:r[o],close:l[o]});a.value!==void 0&&this.result.push(a.value)})}nextValue(e){let r=this.generator.next(e);if(r.value!==void 0)return r.value}}Ce.calculate=f;class be extends te{constructor(e){super(e);let r=e.low,t=e.high,l=e.close,o=e.period,a=this.format;if(r.length!==t.length||t.length!==l.length)throw'Inputs(low,high, close) not of equal size';this.result=[],this.generator=function*(){let e,r,t=1,l=new Z(o,!0,!1),n=new Z(o,!1,!0);var s=yield;for(let u;;){if(l.push(s.high),n.push(s.low),t<o){t++,s=yield;continue}e=n.periodLow,r=l.periodHigh,u=a(-100*((r-s.close)/(r-e))),s=yield u}}(),this.generator.next(),r.forEach((e,o)=>{var a=this.generator.next({high:t[o],low:r[o],close:l[o]});a.value!==void 0&&this.result.push(a.value)})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return this.format(r.value)}}be.calculate=I;class qe extends te{constructor(e){super(e);var r=e.high,t=e.low,l=e.close,o=e.volume;if(t.length!==r.length||r.length!==l.length||r.length!==o.length)throw'Inputs(low,high, close, volumes) not of equal size';this.result=[],this.generator=function*(){var e,r=0;for(e=yield;;){let t=(e.close-e.low-(e.high-e.close))/(e.high-e.low);t=isNaN(t)?1:t;let l=t*e.volume;r+=l,e=yield Math.round(r)}}(),this.generator.next(),r.forEach((e,r)=>{var a={high:e,low:t[r],close:l[r],volume:o[r]},n=this.generator.next(a);n.value!=void 0&&this.result.push(n.value)})}nextValue(e){return this.generator.next(e).value}}qe.calculate=_;class Ee extends te{constructor(e){super(e);var r=e.close,t=e.volume;this.result=[],this.generator=function*(){var e,r,t=0;for(e=yield,e.close&&'number'==typeof e.close&&(r=e.close,e=yield);;)r<e.close?t+=e.volume:e.close<r&&(t-=e.volume),r=e.close,e=yield t}(),this.generator.next(),r.forEach((e,l)=>{let o={close:r[l],volume:t[l]},a=this.generator.next(o);a.value!=void 0&&this.result.push(a.value)})}nextValue(e){return this.generator.next(e).value}}Ee.calculate=P;class Se extends te{constructor(e){super(e);let r=e.values,t=e.period,l=this.format,o=new oe({period:t,values:[],format:(e)=>e}),a=new oe({period:t,values:[],format:(e)=>e}),n=new oe({period:t,values:[],format:(e)=>e}),s=new Ie({period:1,values:[],format:(e)=>e});this.result=[],this.generator=function*(){for(let e=yield;;){let r=o.nextValue(e),t=r?a.nextValue(r):void 0,u=t?n.nextValue(t):void 0,i=u?s.nextValue(u):void 0;e=yield i?l(i):void 0}}(),this.generator.next(),r.forEach((e)=>{let r=this.generator.next(e);r.value!==void 0&&this.result.push(r.value)})}nextValue(e){let r=this.generator.next(e);if(r.value!==void 0)return r.value}}Se.calculate=C;class Ve extends te{constructor(e){super(e);var r=e.close,t=e.volume,l=e.period||1;if(t.length!==r.length)throw'Inputs(volume, close) not of equal size';let o=new oe({values:[],period:l});this.result=[],this.generator=function*(){var e=yield,r=yield;for(let t;;)t=(r.close-e.close)*r.volume,e=r,r=yield o.nextValue(t)}(),this.generator.next(),t.forEach((e,l)=>{var o=this.generator.next({close:r[l],volume:t[l]});o.value!=void 0&&this.result.push(o.value)})}nextValue(e){let r=this.generator.next(e).value;if(r!=void 0)return r}}Ve.calculate=b;class Ae extends te{constructor(e){super(e);var r=e.low,t=e.high,l=e.close,o=e.period,a=this.format;var n=new Z(o),s=new le({period:o,values:[],format:(e)=>e});if(r.length!==t.length||t.length!==l.length)throw'Inputs(low,high, close) not of equal size';this.result=[],this.generator=function*(){for(var e=yield;;){let r=(e.high+e.low+e.close)/3;n.push(r);let t,l=s.nextValue(r),a=null,u=0;if(l!=void 0){for(let e of n.iterator())u+=J(e-l);a=u/o,t=(r-l)/(.015*a)}e=yield t}}(),this.generator.next(),r.forEach((e,o)=>{var a=this.generator.next({high:t[o],low:r[o],close:l[o]});a.value!=void 0&&this.result.push(a.value)})}nextValue(e){let r=this.generator.next(e).value;if(r!=void 0)return r}}Ae.calculate=q;class ke extends te{constructor(e){super(e);var r=e.high,t=e.low,l=e.fastPeriod,o=e.slowPeriod,a=new le({values:[],period:o}),n=new le({values:[],period:l});this.result=[],this.generator=function*(){var e,r,t,l,o;for(r=yield;;)t=(r.high+r.low)/2,l=a.nextValue(t),o=n.nextValue(t),void 0!==l&&void 0!==o&&(e=o-l),r=yield e}(),this.generator.next(),r.forEach((e,r)=>{var l={high:e,low:t[r]},o=this.generator.next(l);o.value!=void 0&&this.result.push(this.format(o.value))})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return this.format(r.value)}}ke.calculate=E;class Be extends te{constructor(e){super(e);var r=e.low,t=e.high,l=e.close,o=e.volume,a=this.format;if(r.length!==t.length||t.length!==l.length)throw'Inputs(low,high, close) not of equal size';this.result=[],this.generator=function*(){var e=yield;for(let r=0,t=0;;){let l=(e.high+e.low+e.close)/3,o=e.volume*l;r+=o,t+=e.volume,e=yield r/t}}(),this.generator.next(),r.forEach((e,a)=>{var n=this.generator.next({high:t[a],low:r[a],close:l[a],volume:o[a]});n.value!=void 0&&this.result.push(n.value)})}nextValue(e){let r=this.generator.next(e).value;if(r!=void 0)return r}}Be.calculate=S;class He extends te{constructor(e){super(e);var r=e.high,t=e.low,l=e.close,o=e.open,a=e.volume,n=e.noOfBars;if(t.length!==r.length||r.length!==l.length||r.length!==a.length)throw'Inputs(low,high, close, volumes) not of equal size';this.result=[];var s=N(...r,...t,...l,...o),u=X(...r,...t,...l,...o),i=u;for(let h=0;h<n;h++){let e=i,h=e+(s-u)/n;i=h;let p=0,g=0,v=0;for(let n=0;n<r.length;n++){let s=t[n],u=r[n],i=o[n],d=l[n],c=a[n];V(e,h,s,u)&&(v+=c,i>d?g+=c:p+=c)}this.result.push({rangeStart:e,rangeEnd:h,bullishVolume:p,bearishVolume:g,totalVolume:v})}}nextValue(){throw'Next value not supported for volume profile'}}He.calculate=A;class ze extends te{constructor(e){super(e),this.result=[],this.generator=function*(){for(let e=yield;;)e=yield(e.high+e.low+e.close)/3}(),this.generator.next(),e.low.forEach((r,t)=>{var l=this.generator.next({high:e.high[t],low:e.low[t],close:e.close[t]});this.result.push(l.value)})}nextValue(e){var r=this.generator.next(e).value;return r}}ze.calculate=function(e){te.reverseInputs(e);var r=new ze(e).result;return e.reversedInput&&r.reverse(),te.reverseInputs(e),r};class Le extends te{constructor(e){super(e);var r=e.high,t=e.low,l=e.close,o=e.volume,a=e.period,n=new ze({low:[],high:[],close:[]}),s=new Z(a,!1,!1,!0),u=new Z(a,!1,!1,!0);if(t.length!==r.length||r.length!==l.length||r.length!==o.length)throw'Inputs(low,high, close, volumes) not of equal size';this.result=[],this.generator=function*(){var e,r,t,l,o,i,h=0;let p=null,g=null;for(r=yield,t=r.close,r=yield;;){var{high:v,low:d,close:c,volume:m}=r,x=0,w=0;p=n.nextValue({high:v,low:d,close:c}),h=p*m,null!=p&&null!=g&&(p>g?x=h:w=h,s.push(x),u.push(w),l=s.periodSum,i=u.periodSum,s.totalPushed>=a&&s.totalPushed>=a&&(o=l/i,e=100-100/(1+o))),g=p,r=yield e}}(),this.generator.next(),r.forEach((e,r)=>{var a={high:e,low:t[r],close:l[r],volume:o[r]},n=this.generator.next(a);n.value!=void 0&&this.result.push(parseFloat(n.value.toFixed(2)))})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return parseFloat(r.value.toFixed(2))}}Le.calculate=k;class Te extends te{constructor(e){super(e);let r=e.values,t=e.rsiPeriod,l=e.stochasticPeriod,o=e.kPeriod,a=e.dPeriod,n=this.format;this.result=[],this.generator=function*(){let e,r,n,s,u=new he({period:t,values:[]}),i=new Ce({period:l,high:[],low:[],close:[],signalPeriod:o}),h=new le({period:a,values:[],format:(e)=>e});for(var p=yield;;){if(e=u.nextValue(p),void 0!==e){var g={high:e,low:e,close:e};r=i.nextValue(g),void 0!==r&&void 0!==r.d&&(n=h.nextValue(r.d),void 0!==n&&(s={stochRSI:r.k,k:r.d,d:n}))}p=yield s}}(),this.generator.next(),r.forEach((e)=>{var r=this.generator.next(e);r.value!==void 0&&this.result.push(r.value)})}nextValue(e){let r=this.generator.next(e);if(r.value!==void 0)return r.value}}Te.calculate=B;class Me extends te{constructor(e){super(e);var r=e.values,t=e.period;this.result=[];var l=new Z(t,!0,!1,!1);this.generator=function*(){var e,r;for(e=yield;;)l.push(e),l.totalPushed>=t&&(r=l.periodHigh),e=yield r}(),this.generator.next(),r.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(r.value)})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return r.value}}Me.calculate=H;class De extends te{constructor(e){super(e);var r=e.values,t=e.period;this.result=[];var l=new Z(t,!1,!0,!1);this.generator=function*(){var e,r;for(e=yield;;)l.push(e),l.totalPushed>=t&&(r=l.periodLow),e=yield r}(),this.generator.next(),r.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(r.value)})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return r.value}}De.calculate=z;class Re extends te{constructor(e){super(e);var r=e.values,t=e.period;this.result=[];var l=new Z(t,!1,!1,!0);this.generator=function*(){var e,r;for(e=yield;;)l.push(e),l.totalPushed>=t&&(r=l.periodSum),e=yield r}(),this.generator.next(),r.forEach((e)=>{var r=this.generator.next(e);r.value!=void 0&&this.result.push(r.value)})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return r.value}}Re.calculate=L;class je extends te{constructor(e){super(e);this.format;let r=e.useATR,t=e.brickSize||0;if(r){let r=c(Object.assign({},e));t=r[r.length-1]}if(this.result=new $,0===t)return void console.error('Not enough data to calculate brickSize for renko when using ATR');let l=0,o=0,a=Infinity,n=0,s=0,u=0;this.generator=function*(){for(let e=yield;;){if(0==l){l=e.close,o=e.high,a=e.low,n=e.close,s=e.volume,u=e.timestamp,e=yield;continue}let r=J(e.close-n),i=J(e.close-l);if(r>=t&&i>=t){let u=r>i?l:n,h={open:u,high:o>e.high?o:e.high,low:a<e.Low?a:e.low,close:u>e.close?u-t:u+t,volume:s+e.volume,timestamp:e.timestamp};l=h.open,o=h.close,a=h.close,n=h.close,s=0,e=yield h}else o=o>e.high?o:e.high,a=a<e.Low?a:e.low,s+=e.volume,u=e.timestamp,e=yield}}(),this.generator.next(),e.low.forEach((r,t)=>{var l=this.generator.next({open:e.open[t],high:e.high[t],low:e.low[t],close:e.close[t],volume:e.volume[t],timestamp:e.timestamp[t]});l.value&&(this.result.open.push(l.value.open),this.result.high.push(l.value.high),this.result.low.push(l.value.low),this.result.close.push(l.value.close),this.result.volume.push(l.value.volume),this.result.timestamp.push(l.value.timestamp))})}nextValue(){return console.error('Cannot calculate next value on Renko, Every value has to be recomputed for every change, use calcualte method'),null}}je.calculate=T;class Oe extends te{constructor(e){super(e);this.format;this.result=new $;let r=null,t=0,l=Infinity,o=0,a=0,n=0;this.generator=function*(){for(let e=yield,s=null;;){if(null==r)r=(e.close+e.open)/2,t=e.high,l=e.low,o=(e.close+e.open+e.high+e.low)/4,a=e.volume||0,n=e.timestamp||0,s={open:r,high:t,low:l,close:o,volume:e.volume||0,timestamp:e.timestamp||0};else{let a=(e.close+e.open+e.high+e.low)/4,n=(r+o)/2,u=N(n,a,e.high),i=X(e.low,n,a);s={close:a,open:n,high:u,low:i,volume:e.volume||0,timestamp:e.timestamp||0},o=a,r=n,t=u,l=i}e=yield s}}(),this.generator.next(),e.low.forEach((r,t)=>{var l=this.generator.next({open:e.open[t],high:e.high[t],low:e.low[t],close:e.close[t],volume:e.volume?e.volume[t]:e.volume,timestamp:e.timestamp?e.timestamp[t]:e.timestamp});l.value&&(this.result.open.push(l.value.open),this.result.high.push(l.value.high),this.result.low.push(l.value.low),this.result.close.push(l.value.close),this.result.volume.push(l.value.volume),this.result.timestamp.push(l.value.timestamp))})}nextValue(e){var r=this.generator.next(e).value;return r}}Oe.calculate=M;class ye{constructor(){}approximateEqual(e,r){let t=1*parseFloat(J(e-r).toPrecision(4)),l=1*parseFloat((1e-3*e).toPrecision(4));return t<=l}logic(){throw'this has to be implemented'}getAllPatternIndex(e){if(e.close.length<this.requiredCount)return console.warn('Data count less than data required for the strategy ',this.name),[];e.reversedInput&&(e.open.reverse(),e.high.reverse(),e.low.reverse(),e.close.reverse());let r=this.logic;return this._generateDataForCandleStick(e).map((e,t)=>r.call(this,e)?t:void 0).filter((e)=>e)}hasPattern(e){if(e.close.length<this.requiredCount)return console.warn('Data count less than data required for the strategy ',this.name),!1;e.reversedInput&&(e.open.reverse(),e.high.reverse(),e.low.reverse(),e.close.reverse());let r=this.logic;return r.call(this,this._getLastDataForCandleStick(e))}_getLastDataForCandleStick(e){let r=this.requiredCount;if(e.close.length===r)return e;else{let t={open:[],high:[],low:[],close:[]},l=0,o=e.close.length-r;for(;l<r;)t.open.push(e.open[o+l]),t.high.push(e.high[o+l]),t.low.push(e.low[o+l]),t.close.push(e.close[o+l]),l++;return t}}_generateDataForCandleStick(e){let r=this.requiredCount,t=e.close.map(function(t,l){let o=0,a={open:[],high:[],low:[],close:[]};for(;o<r;)a.open.push(e.open[l+o]),a.high.push(e.high[l+o]),a.low.push(e.low[l+o]),a.close.push(e.close[l+o]),o++;return a}).filter((t,l)=>l<=e.close.length-r);return t}}class Fe extends ye{constructor(){super(),this.name='MorningStar',this.requiredCount=3}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2];return t<r&&o>u&&o>s&&s<o&&u<o&&i>s&&n<i&&i<h&&h>(r+t)/2}}class Ue extends ye{constructor(){super(),this.name='BullishEngulfingPattern',this.requiredCount=2}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1];return t<r&&r>a&&t>a&&r<n}}class We extends ye{constructor(){super(),this.requiredCount=2,this.name='BullishHarami'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1];return r>a&&t<a&&t<n&&r>u&&l>s}}class Ke extends ye{constructor(){super(),this.requiredCount=2,this.name='BullishHaramiCross'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=this.approximateEqual(a,n);return r>a&&t<a&&t<n&&r>u&&l>s&&i}}class Ge extends ye{constructor(){super(),this.name='Doji',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=this.approximateEqual(r,t),n=a&&this.approximateEqual(r,l),s=a&&this.approximateEqual(t,o);return a&&n==s}}class Xe extends ye{constructor(){super(),this.name='MorningDojiStar',this.requiredCount=3}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2],v=new Ge().hasPattern({open:[a],close:[n],high:[s],low:[u]});return t<r&&v&&i<h&&s<o&&u<o&&i>s&&n<i&&h>(r+t)/2}}class Ne extends ye{constructor(){super(),this.requiredCount=3,this.name='DownsideTasukiGap'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2];return t<r&&n<a&&h>i&&s<o&&a>i&&n<i&&h>a&&h<t}}class Je extends ye{constructor(){super(),this.name='BullishMarubozu',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=this.approximateEqual(t,l)&&this.approximateEqual(o,r)&&r<t&&r<l;return a}}class Qe extends ye{constructor(){super(),this.requiredCount=2,this.name='PiercingLine'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1];return u<o&&t<r&&o>a&&n>(r+t)/2&&n>a}}class Ye extends ye{constructor(){super(),this.name='ThreeWhiteSoldiers',this.requiredCount=3}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2];return s>l&&p>s&&r<t&&a<n&&i<h&&t>a&&a<l&&s>i&&i<n}}class Ze extends ye{constructor(){super(),this.name='BullishHammerStick',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=t>r;return a=a&&this.approximateEqual(t,l),a=a&&t-r<=2*(r-o),a}}class $e extends ye{constructor(){super(),this.name='BullishInvertedHammerStick',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=t>r;return a=a&&this.approximateEqual(r,o),a=a&&t-r<=2*(l-t),a}}class er extends ye{constructor(){super(),this.name='BearishHammerStick',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=r>t;return a=a&&this.approximateEqual(r,l),a=a&&r-t<=2*(t-o),a}}class rr extends ye{constructor(){super(),this.name='BearishInvertedHammerStick',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=r>t;return a=a&&this.approximateEqual(t,o),a=a&&r-t<=2*(l-r),a}}class tr extends ye{constructor(){super(),this.name='HammerPattern',this.requiredCount=5}logic(e){let r=this.downwardTrend(e);return r=r&&this.includesHammer(e),r=r&&this.hasConfirmation(e),r}downwardTrend(e,r=!0){let t=r?3:4,l=u({values:e.close.slice(0,t),period:t-1}),o=i({values:e.close.slice(0,t),period:t-1});return o>l}includesHammer(e,r=!0){let t=r?3:4,l=r?4:void 0,o={open:e.open.slice(t,l),close:e.close.slice(t,l),low:e.low.slice(t,l),high:e.high.slice(t,l)},a=j(o);return a=a||O(o),a=a||D(o),a=a||R(o),a}hasConfirmation(e){let r={open:e.open[3],close:e.close[3],low:e.low[3],high:e.high[3]},t={open:e.open[4],close:e.close[4],low:e.low[4],high:e.high[4]},l=t.open<t.close;return l&&r.close<t.close}}class lr extends tr{constructor(){super(),this.name='HammerPatternUnconfirmed'}logic(e){let r=this.downwardTrend(e,!1);return r=r&&this.includesHammer(e,!1),r}}class or extends ye{constructor(){super(),this.name='TweezerBottom',this.requiredCount=5}logic(e){return this.downwardTrend(e)&&e.low[3]==e.low[4]}downwardTrend(e){let r=u({values:e.close.slice(0,3),period:2}),t=i({values:e.close.slice(0,3),period:2});return t>r}}let ar=[new Ue,new Ne,new We,new Ke,new Xe,new Fe,new Je,new Qe,new Ye,new Ze,new $e,new tr,new lr,new or];class nr extends ye{constructor(){super(),this.name='Bullish Candlesticks'}hasPattern(e){return ar.reduce(function(r,t){let l=t.hasPattern(e);return r||l},!1)}}class sr extends ye{constructor(){super(),this.name='BearishEngulfingPattern',this.requiredCount=2}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1];return t>r&&r<a&&t<a&&r>n}}class ur extends ye{constructor(){super(),this.requiredCount=2,this.name='BearishHarami'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1];return r<a&&t>a&&t>n&&r<u&&l>s}}class ir extends ye{constructor(){super(),this.requiredCount=2,this.name='BearishHaramiCross'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=this.approximateEqual(a,n);return r<a&&t>a&&t>n&&r<u&&l>s&&i}}class hr extends ye{constructor(){super(),this.name='EveningDojiStar',this.requiredCount=3}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2],v=new Ge().hasPattern({open:[a],close:[n],high:[s],low:[u]});return t>r&&v&&s>l&&u>l&&i<u&&n>i&&i>h&&h<(r+t)/2}}class pr extends ye{constructor(){super(),this.name='EveningStar',this.requiredCount=3}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2];return t>r&&l<u&&l<s&&s>l&&u>l&&i<u&&n>i&&i>h&&h<(r+t)/2}}class gr extends ye{constructor(){super(),this.name='BearishMarubozu',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=this.approximateEqual(r,l)&&this.approximateEqual(o,t)&&r>t&&r>o;return a}}class vr extends ye{constructor(){super(),this.name='ThreeBlackCrows',this.requiredCount=3}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2];return o>u&&u>g&&r>t&&a>n&&i>h&&r>a&&a>t&&a>i&&i>n}}class dr extends ye{constructor(){super(),this.name='HangingMan',this.requiredCount=5}logic(e){let r=this.upwardTrend(e);return r=r&&this.includesHammer(e),r=r&&this.hasConfirmation(e),r}upwardTrend(e,r=!0){let t=r?3:4,l=u({values:e.close.slice(0,t),period:t-1}),o=i({values:e.close.slice(0,t),period:t-1});return l>o}includesHammer(e,r=!0){let t=r?3:4,l=r?4:void 0,o={open:e.open.slice(t,l),close:e.close.slice(t,l),low:e.low.slice(t,l),high:e.high.slice(t,l)},a=j(o);return a=a||D(o),a}hasConfirmation(e){let r={open:e.open[3],close:e.close[3],low:e.low[3],high:e.high[3]},t={open:e.open[4],close:e.close[4],low:e.low[4],high:e.high[4]},l=t.open>t.close;return l&&r.close>t.close}}class cr extends dr{constructor(){super(),this.name='HangingManUnconfirmed'}logic(e){let r=this.upwardTrend(e,!1);return r=r&&this.includesHammer(e,!1),r}}class mr extends ye{constructor(){super(),this.name='ShootingStar',this.requiredCount=5}logic(e){let r=this.upwardTrend(e);return r=r&&this.includesHammer(e),r=r&&this.hasConfirmation(e),r}upwardTrend(e,r=!0){let t=r?3:4,l=u({values:e.close.slice(0,t),period:t-1}),o=i({values:e.close.slice(0,t),period:t-1});return l>o}includesHammer(e,r=!0){let t=r?3:4,l=r?4:void 0,o={open:e.open.slice(t,l),close:e.close.slice(t,l),low:e.low.slice(t,l),high:e.high.slice(t,l)},a=O(o);return a=a||R(o),a}hasConfirmation(e){let r={open:e.open[3],close:e.close[3],low:e.low[3],high:e.high[3]},t={open:e.open[4],close:e.close[4],low:e.low[4],high:e.high[4]},l=t.open>t.close;return l&&r.close>t.close}}class xr extends mr{constructor(){super(),this.name='ShootingStarUnconfirmed'}logic(e){let r=this.upwardTrend(e,!1);return r=r&&this.includesHammer(e,!1),r}}class wr extends ye{constructor(){super(),this.name='TweezerTop',this.requiredCount=5}logic(e){return this.upwardTrend(e)&&e.high[3]==e.high[4]}upwardTrend(e){let r=u({values:e.close.slice(0,3),period:2}),t=i({values:e.close.slice(0,3),period:2});return r>t}}let fr=[new sr,new ur,new ir,new hr,new pr,new gr,new vr,new er,new rr,new dr,new cr,new mr,new xr,new wr];class Ir extends ye{constructor(){super(),this.name='Bearish Candlesticks'}hasPattern(e){return fr.reduce(function(r,t){return r||t.hasPattern(e)},!1)}}class _r extends ye{constructor(){super(),this.name='AbandonedBaby',this.requiredCount=3}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1],i=e.open[2],h=e.close[2],p=e.high[2],g=e.low[2],v=new Ge().hasPattern({open:[a],close:[n],high:[s],low:[u]});return t<r&&v&&s<o&&g>s&&h>i&&p<r}}class Pr extends ye{constructor(){super(),this.name='DarkCloudCover',this.requiredCount=2}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=e.open[1],n=e.close[1],s=e.high[1],u=e.low[1];return t>r&&n<a&&a>l&&n<(t+r)/2&&n>r}}class Cr extends ye{constructor(){super(),this.requiredCount=1,this.name='DragonFlyDoji'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=this.approximateEqual(r,t),n=a&&this.approximateEqual(r,l),s=a&&this.approximateEqual(t,o);return a&&n&&!s}}class br extends ye{constructor(){super(),this.requiredCount=1,this.name='GraveStoneDoji'}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=this.approximateEqual(r,t),n=a&&this.approximateEqual(r,l),s=a&&this.approximateEqual(t,o);return a&&s&&!n}}class qr extends ye{constructor(){super(),this.name='BullishSpinningTop',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=J(t-r),n=J(l-t),s=J(r-o);return a<n&&a<s}}class Er extends ye{constructor(){super(),this.name='BearishSpinningTop',this.requiredCount=1}logic(e){let r=e.open[0],t=e.close[0],l=e.high[0],o=e.low[0],a=J(t-r),n=J(l-r),s=J(l-o);return a<n&&a<s}}class Sr extends te{constructor(e){super(e),this.result=[];var r=Object.assign({},{conversionPeriod:9,basePeriod:26,spanPeriod:52,displacement:26},e),t=new Z(2*r.conversionPeriod,!0,!0,!1),l=new Z(2*r.basePeriod,!0,!0,!1),o=new Z(2*r.spanPeriod,!0,!0,!1);this.generator=function*(){let e,a,n=N(r.conversionPeriod,r.basePeriod,r.spanPeriod,r.displacement),s=1;for(a=yield;;){if(t.push(a.high),t.push(a.low),l.push(a.high),l.push(a.low),o.push(a.high),o.push(a.low),s<n)s++;else{let r=(t.periodHigh+t.periodLow)/2,a=(l.periodHigh+l.periodLow)/2,n=(o.periodHigh+o.periodLow)/2;e={conversion:r,base:a,spanA:(r+a)/2,spanB:n}}a=yield e}}(),this.generator.next(),e.low.forEach((r,t)=>{var l=this.generator.next({high:e.high[t],low:e.low[t]});l.value&&this.result.push(l.value)})}nextValue(e){return this.generator.next(e).value}}Sr.calculate=y;class Vr extends te{constructor(e){super(e);var r,t=e.useSMA?le:oe,l=new t({period:e.maPeriod,values:[],format:(e)=>e}),o=new fe({period:e.atrPeriod,high:[],low:[],close:[],format:(e)=>e});this.result=[],this.generator=function*(){var t;for(r=yield;;){var{close:a}=r,n=l.nextValue(a),s=o.nextValue(r);n!=void 0&&s!=void 0&&(t={middle:n,upper:n+e.multiplier*s,lower:n-e.multiplier*s}),r=yield t}}(),this.generator.next();var a=e.high;a.forEach((r,t)=>{var l={high:r,low:e.low[t],close:e.close[t]},o=this.generator.next(l);o.value!=void 0&&this.result.push(o.value)})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return r.value}}Vr.calculate=F;class Ar extends te{constructor(e){super(e);var r=e.high,t=e.low,l=e.close;this.result=[];var o=new fe({period:e.period,high:[],low:[],close:[],format:(e)=>e}),a=new Z(2*e.period,!0,!0,!1);this.generator=function*(){for(var r,t,l=yield;;){var{high:n,low:s}=l;a.push(n),a.push(s),t=o.nextValue(l),a.totalPushed>=2*e.period&&t!=void 0&&(r={exitLong:a.periodHigh-t*e.multiplier,exitShort:a.periodLow+t*e.multiplier}),l=yield r}}(),this.generator.next(),r.forEach((e,r)=>{var o={high:e,low:t[r],close:l[r]},a=this.generator.next(o);a.value!=void 0&&this.result.push(a.value)})}nextValue(e){var r=this.generator.next(e);if(r.value!=void 0)return r.value}}Ar.calculate=U;class kr extends te{constructor(e){super(e),this.lineA=e.lineA,this.lineB=e.lineB;var r=[],t=[];this.generator=function*(){for(var e=yield,l=!1;;){r.unshift(e.valueA),t.unshift(e.valueB),l=e.valueA>e.valueB;for(var o=1;!0==l&&r[o]>=t[o];)r[o]>t[o]?l=!1:r[o]<t[o]?l=!0:r[o]===t[o]&&(o+=1);!0==l&&(r=[e.valueA],t=[e.valueB]),e=yield l}}(),this.generator.next(),this.result=[],this.lineA.forEach((e,r)=>{var t=this.generator.next({valueA:this.lineA[r],valueB:this.lineB[r]});t.value!==void 0&&this.result.push(t.value)})}static reverseInputs(e){e.reversedInput&&(e.lineA?e.lineA.reverse():void 0,e.lineB?e.lineB.reverse():void 0)}nextValue(e,r){return this.generator.next({valueA:e,valueB:r}).value}}kr.calculate=W;class Br extends te{constructor(e){super(e),this.lineA=e.lineA,this.lineB=e.lineB;var r=[],t=[];this.generator=function*(){for(var e=yield,l=!1;;){r.unshift(e.valueA),t.unshift(e.valueB),l=e.valueA<e.valueB;for(var o=1;!0==l&&r[o]<=t[o];)r[o]<t[o]?l=!1:r[o]>t[o]?l=!0:r[o]===t[o]&&(o+=1);!0==l&&(r=[e.valueA],t=[e.valueB]),e=yield l}}(),this.generator.next(),this.result=[],this.lineA.forEach((e,r)=>{var t=this.generator.next({valueA:this.lineA[r],valueB:this.lineB[r]});t.value!==void 0&&this.result.push(t.value)})}static reverseInputs(e){e.reversedInput&&(e.lineA?e.lineA.reverse():void 0,e.lineB?e.lineB.reverse():void 0)}nextValue(e,r){return this.generator.next({valueA:e,valueB:r}).value}}Br.calculate=K;let Hr=G();e.getAvailableIndicators=G,e.AvailableIndicators=Hr,e.FixedSizeLinkedList=Z,e.CandleData=class{},e.CandleList=$,e.sma=l,e.SMA=le,e.ema=o,e.EMA=oe,e.wma=a,e.WMA=ae,e.wema=n,e.WEMA=ne,e.macd=s,e.MACD=se,e.rsi=h,e.RSI=he,e.bollingerbands=g,e.BollingerBands=ge,e.adx=d,e.ADX=we,e.atr=c,e.ATR=fe,e.truerange=v,e.TrueRange=me,e.roc=m,e.ROC=Ie,e.kst=x,e.KST=_e,e.psar=w,e.PSAR=Pe,e.stochastic=f,e.Stochastic=Ce,e.williamsr=I,e.WilliamsR=be,e.adl=_,e.ADL=qe,e.obv=P,e.OBV=Ee,e.trix=C,e.TRIX=Se,e.forceindex=b,e.ForceIndex=Ve,e.cci=q,e.CCI=Ae,e.awesomeoscillator=E,e.AwesomeOscillator=ke,e.vwap=S,e.VWAP=Be,e.volumeprofile=A,e.VolumeProfile=He,e.mfi=k,e.MFI=Le,e.stochasticrsi=B,e.StochasticRSI=Te,e.averagegain=u,e.AverageGain=ue,e.averageloss=i,e.AverageLoss=ie,e.sd=p,e.SD=pe,e.highest=H,e.Highest=Me,e.lowest=z,e.Lowest=De,e.sum=L,e.Sum=Re,e.renko=T,e.HeikinAshi=Oe,e.heikinashi=M,e.bullish=function(e){return new nr().hasPattern(e)},e.bearish=function(e){return new Ir().hasPattern(e)},e.abandonedbaby=function(e){return new _r().hasPattern(e)},e.doji=function(e){return new Ge().hasPattern(e)},e.bearishengulfingpattern=function(e){return new sr().hasPattern(e)},e.bullishengulfingpattern=function(e){return new Ue().hasPattern(e)},e.darkcloudcover=function(e){return new Pr().hasPattern(e)},e.downsidetasukigap=function(e){return new Ne().hasPattern(e)},e.dragonflydoji=function(e){return new Cr().hasPattern(e)},e.gravestonedoji=function(e){return new br().hasPattern(e)},e.bullishharami=function(e){return new We().hasPattern(e)},e.bearishharami=function(e){return new ur().hasPattern(e)},e.bullishharamicross=function(e){return new Ke().hasPattern(e)},e.bearishharamicross=function(e){return new ir().hasPattern(e)},e.eveningdojistar=function(e){return new hr().hasPattern(e)},e.eveningstar=function(e){return new pr().hasPattern(e)},e.morningdojistar=function(e){return new Xe().hasPattern(e)},e.morningstar=function(e){return new Fe().hasPattern(e)},e.bullishmarubozu=function(e){return new Je().hasPattern(e)},e.bearishmarubozu=function(e){return new gr().hasPattern(e)},e.piercingline=function(e){return new Qe().hasPattern(e)},e.bullishspinningtop=function(e){return new qr().hasPattern(e)},e.bearishspinningtop=function(e){return new Er().hasPattern(e)},e.threeblackcrows=function(e){return new vr().hasPattern(e)},e.threewhitesoldiers=function(e){return new Ye().hasPattern(e)},e.bullishhammerstick=D,e.bearishhammerstick=j,e.bullishinvertedhammerstick=R,e.bearishinvertedhammerstick=O,e.hammerpattern=function(e){return new tr().hasPattern(e)},e.hammerpatternunconfirmed=function(e){return new lr().hasPattern(e)},e.hangingman=function(e){return new dr().hasPattern(e)},e.hangingmanunconfirmed=function(e){return new cr().hasPattern(e)},e.shootingstar=function(e){return new mr().hasPattern(e)},e.shootingstarunconfirmed=function(e){return new xr().hasPattern(e)},e.tweezertop=function(e){return new wr().hasPattern(e)},e.tweezerbottom=function(e){return new or().hasPattern(e)},e.fibonacciretracement=function(e,r){let t,l=[0,23.6,38.2,50,61.8,78.6,100,127.2,161.8,261.8,423.6];return t=e<r?l.map(function(t){let l=r-J(e-r)*t/100;return 0<l?l:0}):l.map(function(t){let l=r+J(e-r)*t/100;return 0<l?l:0}),t},e.ichimokucloud=y,e.IchimokuCloud=Sr,e.keltnerchannels=F,e.KeltnerChannels=Vr,e.KeltnerChannelsInput=class extends re{constructor(){super(...arguments),this.maPeriod=20,this.atrPeriod=10,this.useSMA=!1,this.multiplier=1}},e.KeltnerChannelsOutput=class extends re{},e.chandelierexit=U,e.ChandelierExit=Ar,e.ChandelierExitInput=class extends re{constructor(){super(...arguments),this.period=22,this.multiplier=3}},e.ChandelierExitOutput=class extends re{},e.crossUp=W,e.CrossUp=kr,e.crossDown=K,e.CrossDown=Br,e.setConfig=function(e,r){ee[e]=r},e.getConfig=r})(this.window=this.window||{});
