{"version": 3, "file": "index.js", "sources": ["../lib/Utils/LinkedList.js", "../lib/Utils/FixedSizeLinkedList.js", "../lib/StockData.js", "../lib/config.js", "../lib/Utils/NumberFormatter.js", "../lib/indicator/indicator.js", "../lib/moving_averages/SMA.js", "../lib/moving_averages/EMA.js", "../lib/moving_averages/WMA.js", "../lib/moving_averages/WEMA.js", "../lib/moving_averages/MACD.js", "../lib/Utils/AverageGain.js", "../lib/Utils/AverageLoss.js", "../lib/oscillators/RSI.js", "../lib/Utils/SD.js", "../lib/volatility/BollingerBands.js", "../lib/moving_averages/WilderSmoothing.js", "../lib/directionalmovement/MinusDM.js", "../lib/directionalmovement/PlusDM.js", "../lib/directionalmovement/TrueRange.js", "../lib/directionalmovement/ADX.js", "../lib/directionalmovement/ATR.js", "../lib/momentum/ROC.js", "../lib/momentum/KST.js", "../lib/momentum/PSAR.js", "../lib/momentum/Stochastic.js", "../lib/momentum/WilliamsR.js", "../lib/volume/ADL.js", "../lib/volume/OBV.js", "../lib/momentum/TRIX.js", "../lib/volume/ForceIndex.js", "../lib/oscillators/CCI.js", "../lib/oscillators/AwesomeOscillator.js", "../lib/volume/VWAP.js", "../lib/volume/VolumeProfile.js", "../lib/chart_types/TypicalPrice.js", "../lib/volume/MFI.js", "../lib/momentum/StochasticRSI.js", "../lib/Utils/Highest.js", "../lib/Utils/Lowest.js", "../lib/Utils/Sum.js", "../lib/chart_types/Renko.js", "../lib/chart_types/HeikinAshi.js", "../lib/candlestick/CandlestickFinder.js", "../lib/candlestick/MorningStar.js", "../lib/candlestick/BullishEngulfingPattern.js", "../lib/candlestick/BullishHarami.js", "../lib/candlestick/BullishHaramiCross.js", "../lib/candlestick/Doji.js", "../lib/candlestick/MorningDojiStar.js", "../lib/candlestick/DownsideTasukiGap.js", "../lib/candlestick/BullishMarubozu.js", "../lib/candlestick/PiercingLine.js", "../lib/candlestick/ThreeWhiteSoldiers.js", "../lib/candlestick/BullishHammerStick.js", "../lib/candlestick/BullishInvertedHammerStick.js", "../lib/candlestick/BearishHammerStick.js", "../lib/candlestick/BearishInvertedHammerStick.js", "../lib/candlestick/HammerPattern.js", "../lib/candlestick/HammerPatternUnconfirmed.js", "../lib/candlestick/TweezerBottom.js", "../lib/candlestick/Bullish.js", "../lib/candlestick/BearishEngulfingPattern.js", "../lib/candlestick/BearishHarami.js", "../lib/candlestick/BearishHaramiCross.js", "../lib/candlestick/EveningDojiStar.js", "../lib/candlestick/EveningStar.js", "../lib/candlestick/BearishMarubozu.js", "../lib/candlestick/ThreeBlackCrows.js", "../lib/candlestick/HangingMan.js", "../lib/candlestick/HangingManUnconfirmed.js", "../lib/candlestick/ShootingStar.js", "../lib/candlestick/ShootingStarUnconfirmed.js", "../lib/candlestick/TweezerTop.js", "../lib/candlestick/Bearish.js", "../lib/candlestick/AbandonedBaby.js", "../lib/candlestick/DarkCloudCover.js", "../lib/candlestick/DragonFlyDoji.js", "../lib/candlestick/GraveStoneDoji.js", "../lib/candlestick/BullishSpinningTop.js", "../lib/candlestick/BearishSpinningTop.js", "../lib/drawingtools/fibonacci.js", "../lib/ichimoku/IchimokuCloud.js", "../lib/volatility/KeltnerChannels.js", "../lib/volatility/ChandelierExit.js", "../lib/Utils/CrossUp.js", "../lib/Utils/CrossDown.js", "../index.js"], "sourcesContent": ["class Item {\n    constructor(data, prev, next) {\n        this.next = next;\n        if (next)\n            next.prev = this;\n        this.prev = prev;\n        if (prev)\n            prev.next = this;\n        this.data = data;\n    }\n}\nexport class LinkedList {\n    constructor() {\n        this._length = 0;\n    }\n    get head() {\n        return this._head && this._head.data;\n    }\n    get tail() {\n        return this._tail && this._tail.data;\n    }\n    get current() {\n        return this._current && this._current.data;\n    }\n    get length() {\n        return this._length;\n    }\n    push(data) {\n        this._tail = new Item(data, this._tail);\n        if (this._length === 0) {\n            this._head = this._tail;\n            this._current = this._head;\n            this._next = this._head;\n        }\n        this._length++;\n    }\n    pop() {\n        var tail = this._tail;\n        if (this._length === 0) {\n            return;\n        }\n        this._length--;\n        if (this._length === 0) {\n            this._head = this._tail = this._current = this._next = undefined;\n            return tail.data;\n        }\n        this._tail = tail.prev;\n        this._tail.next = undefined;\n        if (this._current === tail) {\n            this._current = this._tail;\n            this._next = undefined;\n        }\n        return tail.data;\n    }\n    shift() {\n        var head = this._head;\n        if (this._length === 0) {\n            return;\n        }\n        this._length--;\n        if (this._length === 0) {\n            this._head = this._tail = this._current = this._next = undefined;\n            return head.data;\n        }\n        this._head = this._head.next;\n        if (this._current === head) {\n            this._current = this._head;\n            this._next = this._current.next;\n        }\n        return head.data;\n    }\n    unshift(data) {\n        this._head = new Item(data, undefined, this._head);\n        if (this._length === 0) {\n            this._tail = this._head;\n            this._next = this._head;\n        }\n        this._length++;\n    }\n    unshiftCurrent() {\n        var current = this._current;\n        if (current === this._head || this._length < 2) {\n            return current && current.data;\n        }\n        // remove\n        if (current === this._tail) {\n            this._tail = current.prev;\n            this._tail.next = undefined;\n            this._current = this._tail;\n        }\n        else {\n            current.next.prev = current.prev;\n            current.prev.next = current.next;\n            this._current = current.prev;\n        }\n        this._next = this._current.next;\n        // unshift\n        current.next = this._head;\n        current.prev = undefined;\n        this._head.prev = current;\n        this._head = current;\n        return current.data;\n    }\n    removeCurrent() {\n        var current = this._current;\n        if (this._length === 0) {\n            return;\n        }\n        this._length--;\n        if (this._length === 0) {\n            this._head = this._tail = this._current = this._next = undefined;\n            return current.data;\n        }\n        if (current === this._tail) {\n            this._tail = current.prev;\n            this._tail.next = undefined;\n            this._current = this._tail;\n        }\n        else if (current === this._head) {\n            this._head = current.next;\n            this._head.prev = undefined;\n            this._current = this._head;\n        }\n        else {\n            current.next.prev = current.prev;\n            current.prev.next = current.next;\n            this._current = current.prev;\n        }\n        this._next = this._current.next;\n        return current.data;\n    }\n    resetCursor() {\n        this._current = this._next = this._head;\n        return this;\n    }\n    next() {\n        var next = this._next;\n        if (next !== undefined) {\n            this._next = next.next;\n            this._current = next;\n            return next.data;\n        }\n    }\n}\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/7/16.\n */\nimport { LinkedList } from './LinkedList';\nexport default class FixedSizeLinkedList extends LinkedList {\n    constructor(size, maintainHigh, maintainLow, maintainSum) {\n        super();\n        this.size = size;\n        this.maintainHigh = maintainHigh;\n        this.maintainLow = maintainLow;\n        this.maintainSum = maintainSum;\n        this.totalPushed = 0;\n        this.periodHigh = 0;\n        this.periodLow = Infinity;\n        this.periodSum = 0;\n        if (!size || typeof size !== 'number') {\n            throw ('Size required and should be a number.');\n        }\n        this._push = this.push;\n        this.push = function (data) {\n            this.add(data);\n            this.totalPushed++;\n        };\n    }\n    add(data) {\n        if (this.length === this.size) {\n            this.lastShift = this.shift();\n            this._push(data);\n            //TODO: FInd a better way\n            if (this.maintainHigh)\n                if (this.lastShift == this.periodHigh)\n                    this.calculatePeriodHigh();\n            if (this.maintainLow)\n                if (this.lastShift == this.periodLow)\n                    this.calculatePeriodLow();\n            if (this.maintainSum) {\n                this.periodSum = this.periodSum - this.lastShift;\n            }\n        }\n        else {\n            this._push(data);\n        }\n        //TODO: FInd a better way\n        if (this.maintainHigh)\n            if (this.periodHigh <= data)\n                (this.periodHigh = data);\n        if (this.maintainLow)\n            if (this.periodLow >= data)\n                (this.periodLow = data);\n        if (this.maintainSum) {\n            this.periodSum = this.periodSum + data;\n        }\n    }\n    *iterator() {\n        this.resetCursor();\n        while (this.next()) {\n            yield this.current;\n        }\n    }\n    calculatePeriodHigh() {\n        this.resetCursor();\n        if (this.next())\n            this.periodHigh = this.current;\n        while (this.next()) {\n            if (this.periodHigh <= this.current) {\n                this.periodHigh = this.current;\n            }\n            ;\n        }\n        ;\n    }\n    calculatePeriodLow() {\n        this.resetCursor();\n        if (this.next())\n            this.periodLow = this.current;\n        while (this.next()) {\n            if (this.periodLow >= this.current) {\n                this.periodLow = this.current;\n            }\n            ;\n        }\n        ;\n    }\n}\n", "export default class StockData {\n    constructor(open, high, low, close, reversedInput) {\n        this.open = open;\n        this.high = high;\n        this.low = low;\n        this.close = close;\n        this.reversedInput = reversedInput;\n    }\n}\nexport class CandleData {\n}\nexport class CandleList {\n    constructor() {\n        this.open = [];\n        this.high = [];\n        this.low = [];\n        this.close = [];\n        this.volume = [];\n        this.timestamp = [];\n    }\n}\n", "let config = {};\nexport function setConfig(key, value) {\n    config[key] = value;\n}\nexport function getConfig(key) {\n    return config[key];\n}\n", "import { getConfig } from '../config';\nexport function format(v) {\n    let precision = getConfig('precision');\n    if (precision) {\n        return parseFloat(v.toPrecision(precision));\n    }\n    return v;\n}\n", "import { format as nf } from '../Utils/NumberFormatter';\nexport class IndicatorInput {\n}\nexport class AllInputs {\n}\nexport class Indicator {\n    constructor(input) {\n        this.format = input.format || nf;\n    }\n    static reverseInputs(input) {\n        if (input.reversedInput) {\n            input.values ? input.values.reverse() : undefined;\n            input.open ? input.open.reverse() : undefined;\n            input.high ? input.high.reverse() : undefined;\n            input.low ? input.low.reverse() : undefined;\n            input.close ? input.close.reverse() : undefined;\n            input.volume ? input.volume.reverse() : undefined;\n            input.timestamp ? input.timestamp.reverse() : undefined;\n        }\n    }\n    getResult() {\n        return this.result;\n    }\n}\n", "//STEP 1. Import Necessary indicator or rather last step\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { LinkedList } from '../Utils/LinkedList';\n//STEP 2. Create the input for the indicator, mandatory should be in the constructor\nexport class MAInput extends IndicatorInput {\n    constructor(period, values) {\n        super();\n        this.period = period;\n        this.values = values;\n    }\n}\n//STEP3. Add class based syntax with export\nexport class SMA extends Indicator {\n    constructor(input) {\n        super(input);\n        this.period = input.period;\n        this.price = input.values;\n        var genFn = (function* (period) {\n            var list = new LinkedList();\n            var sum = 0;\n            var counter = 1;\n            var current = yield;\n            var result;\n            list.push(0);\n            while (true) {\n                if (counter < period) {\n                    counter++;\n                    list.push(current);\n                    sum = sum + current;\n                }\n                else {\n                    sum = sum - list.shift() + current;\n                    result = ((sum) / period);\n                    list.push(current);\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn(this.period);\n        this.generator.next();\n        this.result = [];\n        this.price.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nSMA.calculate = sma;\nexport function sma(input) {\n    Indicator.reverseInputs(input);\n    var result = new SMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n//STEP 6. Run the tests\n", "import { Indicator } from '../indicator/indicator';\nimport { SMA } from './SMA';\nexport class EMA extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var exponent = (2 / (period + 1));\n        var sma;\n        this.result = [];\n        sma = new SMA({ period: period, values: [] });\n        var genFn = (function* () {\n            var tick = yield;\n            var prevEma;\n            while (true) {\n                if (prevEma !== undefined && tick !== undefined) {\n                    prevEma = ((tick - prevEma) * exponent) + prevEma;\n                    tick = yield prevEma;\n                }\n                else {\n                    tick = yield;\n                    prevEma = sma.nextValue(tick);\n                    if (prevEma)\n                        tick = yield prevEma;\n                }\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nEMA.calculate = ema;\nexport function ema(input) {\n    Indicator.reverseInputs(input);\n    var result = new EMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "\"use strict\";\nimport { Indicator } from '../indicator/indicator';\nimport { LinkedList } from '../Utils/LinkedList';\nexport class WMA extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        this.result = [];\n        this.generator = (function* () {\n            let data = new LinkedList();\n            let denominator = period * (period + 1) / 2;\n            while (true) {\n                if ((data.length) < period) {\n                    data.push(yield);\n                }\n                else {\n                    data.resetCursor();\n                    let result = 0;\n                    for (let i = 1; i <= period; i++) {\n                        result = result + (data.next() * i / (denominator));\n                    }\n                    var next = yield result;\n                    data.shift();\n                    data.push(next);\n                }\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick, index) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    //STEP 5. REMOVE GET RESULT FUNCTION\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nWMA.calculate = wma;\n;\nexport function wma(input) {\n    Indicator.reverseInputs(input);\n    var result = new WMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "import { Indicator } from '../indicator/indicator';\nimport { SMA } from './SMA';\nexport class WEMA extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var exponent = 1 / period;\n        var sma;\n        this.result = [];\n        sma = new SMA({ period: period, values: [] });\n        var genFn = (function* () {\n            var tick = yield;\n            var prevEma;\n            while (true) {\n                if (prevEma !== undefined && tick !== undefined) {\n                    prevEma = ((tick - prevEma) * exponent) + prevEma;\n                    tick = yield prevEma;\n                }\n                else {\n                    tick = yield;\n                    prevEma = sma.nextValue(tick);\n                    if (prevEma !== undefined)\n                        tick = yield prevEma;\n                }\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nWEMA.calculate = wema;\nexport function wema(input) {\n    Indicator.reverseInputs(input);\n    var result = new WEMA(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from './SMA';\nimport { EMA } from './EMA';\nexport class MACDInput extends IndicatorInput {\n    constructor(values) {\n        super();\n        this.values = values;\n        this.SimpleMAOscillator = true;\n        this.SimpleMASignal = true;\n    }\n}\nexport class MACDOutput {\n}\nexport class MACD extends Indicator {\n    constructor(input) {\n        super(input);\n        var oscillatorMAtype = input.SimpleMAOscillator ? SMA : EMA;\n        var signalMAtype = input.SimpleMASignal ? SMA : EMA;\n        var fastMAProducer = new oscillatorMAtype({ period: input.fastPeriod, values: [], format: (v) => { return v; } });\n        var slowMAProducer = new oscillatorMAtype({ period: input.slowPeriod, values: [], format: (v) => { return v; } });\n        var signalMAProducer = new signalMAtype({ period: input.signalPeriod, values: [], format: (v) => { return v; } });\n        var format = this.format;\n        this.result = [];\n        this.generator = (function* () {\n            var index = 0;\n            var tick;\n            var MACD, signal, histogram, fast, slow;\n            while (true) {\n                if (index < input.slowPeriod) {\n                    tick = yield;\n                    fast = fastMAProducer.nextValue(tick);\n                    slow = slowMAProducer.nextValue(tick);\n                    index++;\n                    continue;\n                }\n                if (fast && slow) { //Just for typescript to be happy\n                    MACD = fast - slow;\n                    signal = signalMAProducer.nextValue(MACD);\n                }\n                histogram = MACD - signal;\n                tick = yield ({\n                    //fast : fast,\n                    //slow : slow,\n                    MACD: format(MACD),\n                    signal: signal ? format(signal) : undefined,\n                    histogram: isNaN(histogram) ? undefined : format(histogram)\n                });\n                fast = fastMAProducer.nextValue(tick);\n                slow = slowMAProducer.nextValue(tick);\n            }\n        })();\n        this.generator.next();\n        input.values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        return result;\n    }\n    ;\n}\nMACD.calculate = macd;\nexport function macd(input) {\n    Indicator.reverseInputs(input);\n    var result = new MACD(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class AvgGainInput extends IndicatorInput {\n}\nexport class AverageGain extends Indicator {\n    constructor(input) {\n        super(input);\n        let values = input.values;\n        let period = input.period;\n        let format = this.format;\n        this.generator = (function* (period) {\n            var currentValue = yield;\n            var counter = 1;\n            var gainSum = 0;\n            var avgGain;\n            var gain;\n            var lastValue = currentValue;\n            currentValue = yield;\n            while (true) {\n                gain = currentValue - lastValue;\n                gain = gain > 0 ? gain : 0;\n                if (gain > 0) {\n                    gainSum = gainSum + gain;\n                }\n                if (counter < period) {\n                    counter++;\n                }\n                else if (avgGain === undefined) {\n                    avgGain = gainSum / period;\n                }\n                else {\n                    avgGain = ((avgGain * (period - 1)) + gain) / period;\n                }\n                lastValue = currentValue;\n                avgGain = (avgGain !== undefined) ? format(avgGain) : undefined;\n                currentValue = yield avgGain;\n            }\n        })(period);\n        this.generator.next();\n        this.result = [];\n        values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nAverageGain.calculate = averagegain;\nexport function averagegain(input) {\n    Indicator.reverseInputs(input);\n    var result = new AverageGain(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class AvgLossInput extends IndicatorInput {\n}\nexport class AverageLoss extends Indicator {\n    constructor(input) {\n        super(input);\n        let values = input.values;\n        let period = input.period;\n        let format = this.format;\n        this.generator = (function* (period) {\n            var currentValue = yield;\n            var counter = 1;\n            var lossSum = 0;\n            var avgLoss;\n            var loss;\n            var lastValue = currentValue;\n            currentValue = yield;\n            while (true) {\n                loss = lastValue - currentValue;\n                loss = loss > 0 ? loss : 0;\n                if (loss > 0) {\n                    lossSum = lossSum + loss;\n                }\n                if (counter < period) {\n                    counter++;\n                }\n                else if (avgLoss === undefined) {\n                    avgLoss = lossSum / period;\n                }\n                else {\n                    avgLoss = ((avgLoss * (period - 1)) + loss) / period;\n                }\n                lastValue = currentValue;\n                avgLoss = (avgLoss !== undefined) ? format(avgLoss) : undefined;\n                currentValue = yield avgLoss;\n            }\n        })(period);\n        this.generator.next();\n        this.result = [];\n        values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nAverageLoss.calculate = averageloss;\nexport function averageloss(input) {\n    Indicator.reverseInputs(input);\n    var result = new AverageLoss(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/5/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { AverageGain } from '../Utils/AverageGain';\nimport { AverageLoss } from '../Utils/AverageLoss';\nexport class RSIInput extends IndicatorInput {\n}\nexport class RSI extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var values = input.values;\n        var GainProvider = new AverageGain({ period: period, values: [] });\n        var LossProvider = new AverageLoss({ period: period, values: [] });\n        let count = 1;\n        this.generator = (function* (period) {\n            var current = yield;\n            var lastAvgGain, lastAvgLoss, RS, currentRSI;\n            while (true) {\n                lastAvgGain = GainProvider.nextValue(current);\n                lastAvgLoss = LossProvider.nextValue(current);\n                if ((lastAvgGain !== undefined) && (lastAvgLoss !== undefined)) {\n                    if (lastAvgLoss === 0) {\n                        currentRSI = 100;\n                    }\n                    else if (lastAvgGain === 0) {\n                        currentRSI = 0;\n                    }\n                    else {\n                        RS = lastAvgGain / lastAvgLoss;\n                        RS = isNaN(RS) ? 0 : RS;\n                        currentRSI = parseFloat((100 - (100 / (1 + RS))).toFixed(2));\n                    }\n                }\n                count++;\n                current = yield currentRSI;\n            }\n        })(period);\n        this.generator.next();\n        this.result = [];\n        values.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nRSI.calculate = rsi;\nexport function rsi(input) {\n    Indicator.reverseInputs(input);\n    var result = new RSI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\n/**\n * Created by <PERSON>ra<PERSON>dan on 5/7/16.\n */\n\"use strict\";\nexport class SDInput extends IndicatorInput {\n}\n;\nexport class SD extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var sma = new SMA({ period: period, values: [], format: (v) => { return v; } });\n        this.result = [];\n        this.generator = (function* () {\n            var tick;\n            var mean;\n            var currentSet = new LinkedList(period);\n            ;\n            tick = yield;\n            var sd;\n            while (true) {\n                currentSet.push(tick);\n                mean = sma.nextValue(tick);\n                if (mean) {\n                    let sum = 0;\n                    for (let x of currentSet.iterator()) {\n                        sum = sum + (Math.pow((x - mean), 2));\n                    }\n                    sd = Math.sqrt(sum / (period));\n                }\n                tick = yield sd;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var nextResult = this.generator.next(price);\n        if (nextResult.value != undefined)\n            return this.format(nextResult.value);\n    }\n    ;\n}\nSD.calculate = sd;\nexport function sd(input) {\n    Indicator.reverseInputs(input);\n    var result = new SD(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "\"use strict\";\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport { SD } from '../Utils/SD';\nexport class BollingerBandsInput extends IndicatorInput {\n}\n;\nexport class BollingerBandsOutput extends IndicatorInput {\n}\n;\nexport class <PERSON>llingerBands extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        var stdDev = input.stdDev;\n        var format = this.format;\n        var sma, sd;\n        this.result = [];\n        sma = new SMA({ period: period, values: [], format: (v) => { return v; } });\n        sd = new SD({ period: period, values: [], format: (v) => { return v; } });\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var calcSMA;\n            var calcsd;\n            tick = yield;\n            while (true) {\n                calcSMA = sma.nextValue(tick);\n                calcsd = sd.nextValue(tick);\n                if (calcSMA) {\n                    let middle = format(calcSMA);\n                    let upper = format(calcSMA + (calcsd * stdDev));\n                    let lower = format(calcSMA - (calcsd * stdDev));\n                    let pb = format((tick - lower) / (upper - lower));\n                    result = {\n                        middle: middle,\n                        upper: upper,\n                        lower: lower,\n                        pb: pb\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nBollingerBands.calculate = bollingerbands;\nexport function bollingerbands(input) {\n    Indicator.reverseInputs(input);\n    var result = new BollingerBands(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator } from '../indicator/indicator';\nimport { LinkedList } from '../Utils/LinkedList';\n//STEP3. Add class based syntax with export\nexport class WilderSmoothing extends Indicator {\n    constructor(input) {\n        super(input);\n        this.period = input.period;\n        this.price = input.values;\n        var genFn = (function* (period) {\n            var list = new LinkedList();\n            var sum = 0;\n            var counter = 1;\n            var current = yield;\n            var result = 0;\n            while (true) {\n                if (counter < period) {\n                    counter++;\n                    sum = sum + current;\n                    result = undefined;\n                }\n                else if (counter == period) {\n                    counter++;\n                    sum = sum + current;\n                    result = sum;\n                }\n                else {\n                    result = result - (result / period) + current;\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn(this.period);\n        this.generator.next();\n        this.result = [];\n        this.price.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        if (result != undefined)\n            return this.format(result);\n    }\n    ;\n}\nWilderSmoothing.calculate = wildersmoothing;\nexport function wildersmoothing(input) {\n    Indicator.reverseInputs(input);\n    var result = new WilderSmoothing(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n//STEP 6. Run the tests\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n\"use strict\";\nexport class MDMInput extends IndicatorInput {\n}\n;\nexport class MDM extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var format = this.format;\n        if (lows.length != highs.length) {\n            throw ('Inputs(low,high) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var minusDm;\n            var current = yield;\n            var last;\n            while (true) {\n                if (last) {\n                    let upMove = (current.high - last.high);\n                    let downMove = (last.low - current.low);\n                    minusDm = format((downMove > upMove && downMove > 0) ? downMove : 0);\n                }\n                last = current;\n                current = yield minusDm;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index]\n            });\n            if (result.value !== undefined)\n                this.result.push(result.value);\n        });\n    }\n    ;\n    static calculate(input) {\n        Indicator.reverseInputs(input);\n        var result = new MDM(input).result;\n        if (input.reversedInput) {\n            result.reverse();\n        }\n        Indicator.reverseInputs(input);\n        return result;\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\nexport class PDMInput extends IndicatorInput {\n}\n;\nexport class PDM extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var format = this.format;\n        if (lows.length != highs.length) {\n            throw ('Inputs(low,high) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var plusDm;\n            var current = yield;\n            var last;\n            while (true) {\n                if (last) {\n                    let upMove = (current.high - last.high);\n                    let downMove = (last.low - current.low);\n                    plusDm = format((upMove > downMove && upMove > 0) ? upMove : 0);\n                }\n                last = current;\n                current = yield plusDm;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index]\n            });\n            if (result.value !== undefined)\n                this.result.push(result.value);\n        });\n    }\n    ;\n    static calculate(input) {\n        Indicator.reverseInputs(input);\n        var result = new PDM(input).result;\n        if (input.reversedInput) {\n            result.reverse();\n        }\n        Indicator.reverseInputs(input);\n        return result;\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n\"use strict\";\nexport class TrueRangeInput extends IndicatorInput {\n}\n;\nexport class TrueRange extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var format = this.format;\n        if (lows.length != highs.length) {\n            throw ('Inputs(low,high) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var current = yield;\n            var previousClose, result;\n            while (true) {\n                if (previousClose === undefined) {\n                    previousClose = current.close;\n                    current = yield result;\n                }\n                result = Math.max(current.high - current.low, isNaN(Math.abs(current.high - previousClose)) ? 0 : Math.abs(current.high - previousClose), isNaN(Math.abs(current.low - previousClose)) ? 0 : Math.abs(current.low - previousClose));\n                previousClose = current.close;\n                if (result != undefined) {\n                    result = format(result);\n                }\n                current = yield result;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nTrueRange.calculate = truerange;\nexport function truerange(input) {\n    Indicator.reverseInputs(input);\n    var result = new TrueRange(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { WilderSmoothing } from '../moving_averages/WilderSmoothing';\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { MDM } from './MinusDM';\nimport { PDM } from './PlusDM';\nimport { TrueRange } from './TrueRange';\nimport { WEMA } from '../moving_averages/WEMA';\nexport class ADXInput extends IndicatorInput {\n}\n;\nexport class ADXOutput extends IndicatorInput {\n}\n;\nexport class ADX extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var period = input.period;\n        var format = this.format;\n        var plusDM = new PDM({\n            high: [],\n            low: []\n        });\n        var minusDM = new MDM({\n            high: [],\n            low: []\n        });\n        var emaPDM = new WilderSmoothing({ period: period, values: [], format: (v) => { return v; } });\n        var emaMDM = new WilderSmoothing({ period: period, values: [], format: (v) => { return v; } });\n        var emaTR = new WilderSmoothing({ period: period, values: [], format: (v) => { return v; } });\n        var emaDX = new WEMA({ period: period, values: [], format: (v) => { return v; } });\n        var tr = new TrueRange({\n            low: [],\n            high: [],\n            close: [],\n        });\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        ADXOutput;\n        this.generator = (function* () {\n            var tick = yield;\n            var index = 0;\n            var lastATR, lastAPDM, lastAMDM, lastPDI, lastMDI, lastDX, smoothedDX;\n            lastATR = 0;\n            lastAPDM = 0;\n            lastAMDM = 0;\n            while (true) {\n                let calcTr = tr.nextValue(tick);\n                let calcPDM = plusDM.nextValue(tick);\n                let calcMDM = minusDM.nextValue(tick);\n                if (calcTr === undefined) {\n                    tick = yield;\n                    continue;\n                }\n                let lastATR = emaTR.nextValue(calcTr);\n                let lastAPDM = emaPDM.nextValue(calcPDM);\n                let lastAMDM = emaMDM.nextValue(calcMDM);\n                if ((lastATR != undefined) && (lastAPDM != undefined) && (lastAMDM != undefined)) {\n                    lastPDI = (lastAPDM) * 100 / lastATR;\n                    lastMDI = (lastAMDM) * 100 / lastATR;\n                    let diDiff = Math.abs(lastPDI - lastMDI);\n                    let diSum = (lastPDI + lastMDI);\n                    lastDX = (diDiff / diSum) * 100;\n                    smoothedDX = emaDX.nextValue(lastDX);\n                    // console.log(tick.high.toFixed(2), tick.low.toFixed(2), tick.close.toFixed(2) , calcTr.toFixed(2), calcPDM.toFixed(2), calcMDM.toFixed(2), lastATR.toFixed(2), lastAPDM.toFixed(2), lastAMDM.toFixed(2), lastPDI.toFixed(2), lastMDI.toFixed(2), diDiff.toFixed(2), diSum.toFixed(2), lastDX.toFixed(2));\n                }\n                tick = yield { adx: smoothedDX, pdi: lastPDI, mdi: lastMDI };\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value != undefined && result.value.adx != undefined) {\n                this.result.push({ adx: format(result.value.adx), pdi: format(result.value.pdi), mdi: format(result.value.mdi) });\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined && result.adx != undefined) {\n            return { adx: this.format(result.adx), pdi: this.format(result.pdi), mdi: this.format(result.mdi) };\n        }\n    }\n    ;\n}\nADX.calculate = adx;\nexport function adx(input) {\n    Indicator.reverseInputs(input);\n    var result = new ADX(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/8/16.\n */\n\"use strict\";\nimport { WEMA } from '../moving_averages/WEMA';\nimport { TrueRange } from './TrueRange';\nexport class ATRInput extends IndicatorInput {\n}\n;\nexport class ATR extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var period = input.period;\n        var format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        var trueRange = new TrueRange({\n            low: [],\n            high: [],\n            close: []\n        });\n        var wema = new WEMA({ period: period, values: [], format: (v) => { return v; } });\n        this.result = [];\n        this.generator = (function* () {\n            var tick = yield;\n            var avgTrueRange, trange;\n            ;\n            while (true) {\n                trange = trueRange.nextValue({\n                    low: tick.low,\n                    high: tick.high,\n                    close: tick.close\n                });\n                if (trange === undefined) {\n                    avgTrueRange = undefined;\n                }\n                else {\n                    avgTrueRange = wema.nextValue(trange);\n                }\n                tick = yield avgTrueRange;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(format(result.value));\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nATR.calculate = atr;\nexport function atr(input) {\n    Indicator.reverseInputs(input);\n    var result = new ATR(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class ROCInput extends IndicatorInput {\n}\nexport class ROC extends Indicator {\n    constructor(input) {\n        super(input);\n        var period = input.period;\n        var priceArray = input.values;\n        this.result = [];\n        this.generator = (function* () {\n            let index = 1;\n            var pastPeriods = new LinkedList(period);\n            ;\n            var tick = yield;\n            var roc;\n            while (true) {\n                pastPeriods.push(tick);\n                if (index < period) {\n                    index++;\n                }\n                else {\n                    roc = ((tick - pastPeriods.lastShift) / (pastPeriods.lastShift)) * 100;\n                }\n                tick = yield roc;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            var result = this.generator.next(tick);\n            if (result.value != undefined && (!isNaN(result.value))) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    nextValue(price) {\n        var nextResult = this.generator.next(price);\n        if (nextResult.value != undefined && (!isNaN(nextResult.value))) {\n            return this.format(nextResult.value);\n        }\n    }\n    ;\n}\nROC.calculate = roc;\n;\nexport function roc(input) {\n    Indicator.reverseInputs(input);\n    var result = new ROC(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport { ROC } from './ROC';\nexport class KSTInput extends IndicatorInput {\n}\nexport class KSTOutput {\n}\nexport class KST extends Indicator {\n    constructor(input) {\n        super(input);\n        let priceArray = input.values;\n        let rocPer1 = input.ROCPer1;\n        let rocPer2 = input.ROCPer2;\n        let rocPer3 = input.ROCPer3;\n        let rocPer4 = input.ROCPer4;\n        let smaPer1 = input.SMAROCPer1;\n        let smaPer2 = input.SMAROCPer2;\n        let smaPer3 = input.SMAROCPer3;\n        let smaPer4 = input.SMAROCPer4;\n        let signalPeriod = input.signalPeriod;\n        let roc1 = new ROC({ period: rocPer1, values: [] });\n        let roc2 = new ROC({ period: rocPer2, values: [] });\n        let roc3 = new ROC({ period: rocPer3, values: [] });\n        let roc4 = new ROC({ period: rocPer4, values: [] });\n        let sma1 = new SMA({ period: smaPer1, values: [], format: (v) => { return v; } });\n        let sma2 = new SMA({ period: smaPer2, values: [], format: (v) => { return v; } });\n        let sma3 = new SMA({ period: smaPer3, values: [], format: (v) => { return v; } });\n        let sma4 = new SMA({ period: smaPer4, values: [], format: (v) => { return v; } });\n        let signalSMA = new SMA({ period: signalPeriod, values: [], format: (v) => { return v; } });\n        var format = this.format;\n        this.result = [];\n        let firstResult = Math.max(rocPer1 + smaPer1, rocPer2 + smaPer2, rocPer3 + smaPer3, rocPer4 + smaPer4);\n        this.generator = (function* () {\n            let index = 1;\n            let tick = yield;\n            let kst;\n            let RCMA1, RCMA2, RCMA3, RCMA4, signal, result;\n            while (true) {\n                let roc1Result = roc1.nextValue(tick);\n                let roc2Result = roc2.nextValue(tick);\n                let roc3Result = roc3.nextValue(tick);\n                let roc4Result = roc4.nextValue(tick);\n                RCMA1 = (roc1Result !== undefined) ? sma1.nextValue(roc1Result) : undefined;\n                RCMA2 = (roc2Result !== undefined) ? sma2.nextValue(roc2Result) : undefined;\n                RCMA3 = (roc3Result !== undefined) ? sma3.nextValue(roc3Result) : undefined;\n                RCMA4 = (roc4Result !== undefined) ? sma4.nextValue(roc4Result) : undefined;\n                if (index < firstResult) {\n                    index++;\n                }\n                else {\n                    kst = (RCMA1 * 1) + (RCMA2 * 2) + (RCMA3 * 3) + (RCMA4 * 4);\n                }\n                signal = (kst !== undefined) ? signalSMA.nextValue(kst) : undefined;\n                result = kst !== undefined ? {\n                    kst: format(kst),\n                    signal: signal ? format(signal) : undefined\n                } : undefined;\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            let result = this.generator.next(tick);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        let nextResult = this.generator.next(price);\n        if (nextResult.value != undefined)\n            return nextResult.value;\n    }\n    ;\n}\nKST.calculate = kst;\nexport function kst(input) {\n    Indicator.reverseInputs(input);\n    var result = new KST(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\n\"use strict\";\n/*\n  There seems to be a few interpretations of the rules for this regarding which prices.\n  I mean the english from which periods are included. The wording does seem to\n  introduce some discrepancy so maybe that is why. I want to put the author's\n  own description here to reassess this later.\n  ----------------------------------------------------------------------------------------\n  For the first day of entry the SAR is the previous Significant Point\n\n  If long the SP is the lowest price reached while in the previous short trade\n  If short the SP is the highest price reached while in the previous long trade\n\n  If long:\n  Find the difference between the highest price made while in the trade and the SAR for today.\n  Multiple the difference by the AF and ADD the result to today's SAR to obtain the SAR for tomorrow.\n  Use 0.02 for the first AF and increase it by 0.02 on every day that a new high for the trade is made.\n  If a new high is not made continue to use the AF as last increased. Do not increase the AF above .20\n\n  Never move the SAR for tomorrow ABOVE the previous day's LOW or today's LOW.\n  If the SAR is calculated to be ABOVE the previous day's LOW or today's LOW then use the lower low between today and the previous day as the new SAR.\n  Make the next day's calculations based on this SAR.\n\n  If short:\n  Find the difference between the lowest price made while in the trade and the SAR for today.\n  Multiple the difference by the AF and SUBTRACT the result to today's SAR to obtain the SAR for tomorrow.\n  Use 0.02 for the first AF and increase it by 0.02 on every day that a new high for the trade is made.\n  If a new high is not made continue to use the AF as last increased. Do not increase the AF above .20\n\n  Never move the SAR for tomorrow BELOW the previous day's HIGH or today's HIGH.\n  If the SAR is calculated to be BELOW the previous day's HIGH or today's HIGH then use the higher high between today and the previous day as the new SAR. Make the next day's calculations based on this SAR.\n  ----------------------------------------------------------------------------------------\n*/\nexport class PSARInput extends IndicatorInput {\n}\n;\nexport class PSAR extends Indicator {\n    constructor(input) {\n        super(input);\n        let highs = input.high || [];\n        let lows = input.low || [];\n        var genFn = function* (step, max) {\n            let curr, extreme, sar, furthest;\n            let up = true;\n            let accel = step;\n            let prev = yield;\n            while (true) {\n                if (curr) {\n                    sar = sar + accel * (extreme - sar);\n                    if (up) {\n                        sar = Math.min(sar, furthest.low, prev.low);\n                        if (curr.high > extreme) {\n                            extreme = curr.high;\n                            accel = Math.min(accel + step, max);\n                        }\n                        ;\n                    }\n                    else {\n                        sar = Math.max(sar, furthest.high, prev.high);\n                        if (curr.low < extreme) {\n                            extreme = curr.low;\n                            accel = Math.min(accel + step, max);\n                        }\n                    }\n                    if ((up && curr.low < sar) || (!up && curr.high > sar)) {\n                        accel = step;\n                        sar = extreme;\n                        up = !up;\n                        extreme = !up ? curr.low : curr.high;\n                    }\n                }\n                else {\n                    // Randomly setup start values? What is the trend on first tick??\n                    sar = prev.low;\n                    extreme = prev.high;\n                }\n                furthest = prev;\n                if (curr)\n                    prev = curr;\n                curr = yield sar;\n            }\n        };\n        this.result = [];\n        this.generator = genFn(input.step, input.max);\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(input) {\n        let nextResult = this.generator.next(input);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nPSAR.calculate = psar;\nexport function psar(input) {\n    Indicator.reverseInputs(input);\n    var result = new PSAR(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/10/16.\n */\n\"use strict\";\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nimport { SMA } from '../moving_averages/SMA';\nexport class StochasticInput extends IndicatorInput {\n}\n;\nexport class StochasticOutput {\n}\n;\nexport class Stochastic extends Indicator {\n    constructor(input) {\n        super(input);\n        let lows = input.low;\n        let highs = input.high;\n        let closes = input.close;\n        let period = input.period;\n        let signalPeriod = input.signalPeriod;\n        let format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        //%K = (Current Close - Lowest Low)/(Highest High - Lowest Low) * 100\n        //%D = 3-day SMA of %K\n        //\n        //Lowest Low = lowest low for the look-back period\n        //Highest High = highest high for the look-back period\n        //%K is multiplied by 100 to move the decimal point two places\n        this.generator = (function* () {\n            let index = 1;\n            let pastHighPeriods = new LinkedList(period, true, false);\n            let pastLowPeriods = new LinkedList(period, false, true);\n            let dSma = new SMA({\n                period: signalPeriod,\n                values: [],\n                format: (v) => { return v; }\n            });\n            let k, d;\n            var tick = yield;\n            while (true) {\n                pastHighPeriods.push(tick.high);\n                pastLowPeriods.push(tick.low);\n                if (index < period) {\n                    index++;\n                    tick = yield;\n                    continue;\n                }\n                let periodLow = pastLowPeriods.periodLow;\n                k = (tick.close - periodLow) / (pastHighPeriods.periodHigh - periodLow) * 100;\n                k = isNaN(k) ? 0 : k; //This happens when the close, high and low are same for the entire period; Bug fix for \n                d = dSma.nextValue(k);\n                tick = yield {\n                    k: format(k),\n                    d: (d !== undefined) ? format(d) : undefined\n                };\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(input) {\n        let nextResult = this.generator.next(input);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nStochastic.calculate = stochastic;\nexport function stochastic(input) {\n    Indicator.reverseInputs(input);\n    var result = new Stochastic(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class WilliamsRInput extends IndicatorInput {\n}\n;\nexport class WilliamsR extends Indicator {\n    constructor(input) {\n        super(input);\n        let lows = input.low;\n        let highs = input.high;\n        let closes = input.close;\n        let period = input.period;\n        let format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        //%R = (Highest High - Close)/(Highest High - Lowest Low) * -100\n        //Lowest Low = lowest low for the look-back period\n        //Highest High = highest high for the look-back period\n        //%R is multiplied by -100 correct the inversion and move the decimal.\n        this.generator = (function* () {\n            let index = 1;\n            let pastHighPeriods = new LinkedList(period, true, false);\n            let pastLowPeriods = new LinkedList(period, false, true);\n            let periodLow;\n            let periodHigh;\n            var tick = yield;\n            let williamsR;\n            while (true) {\n                pastHighPeriods.push(tick.high);\n                pastLowPeriods.push(tick.low);\n                if (index < period) {\n                    index++;\n                    tick = yield;\n                    continue;\n                }\n                periodLow = pastLowPeriods.periodLow;\n                periodHigh = pastHighPeriods.periodHigh;\n                williamsR = format((periodHigh - tick.close) / (periodHigh - periodLow) * -100);\n                tick = yield williamsR;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((low, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var nextResult = this.generator.next(price);\n        if (nextResult.value != undefined)\n            return this.format(nextResult.value);\n    }\n    ;\n}\nWilliamsR.calculate = williamsr;\nexport function williamsr(input) {\n    Indicator.reverseInputs(input);\n    var result = new WilliamsR(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/17/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class ADLInput extends IndicatorInput {\n}\nexport class ADL extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        var volumes = input.volume;\n        if (!((lows.length === highs.length) && (highs.length === closes.length) && (highs.length === volumes.length))) {\n            throw ('Inputs(low,high, close, volumes) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var result = 0;\n            var tick;\n            tick = yield;\n            while (true) {\n                let moneyFlowMultiplier = ((tick.close - tick.low) - (tick.high - tick.close)) / (tick.high - tick.low);\n                moneyFlowMultiplier = isNaN(moneyFlowMultiplier) ? 1 : moneyFlowMultiplier;\n                let moneyFlowVolume = moneyFlowMultiplier * tick.volume;\n                result = result + moneyFlowVolume;\n                tick = yield Math.round(result);\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n                close: closes[index],\n                volume: volumes[index]\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nADL.calculate = adl;\nexport function adl(input) {\n    Indicator.reverseInputs(input);\n    var result = new ADL(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/17/16.\n */\n\"use strict\";\nexport class OBVInput extends IndicatorInput {\n}\nexport class OBV extends Indicator {\n    constructor(input) {\n        super(input);\n        var closes = input.close;\n        var volumes = input.volume;\n        this.result = [];\n        this.generator = (function* () {\n            var result = 0;\n            var tick;\n            var lastClose;\n            tick = yield;\n            if (tick.close && (typeof tick.close === 'number')) {\n                lastClose = tick.close;\n                tick = yield;\n            }\n            while (true) {\n                if (lastClose < tick.close) {\n                    result = result + tick.volume;\n                }\n                else if (tick.close < lastClose) {\n                    result = result - tick.volume;\n                }\n                lastClose = tick.close;\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        closes.forEach((close, index) => {\n            let tickInput = {\n                close: closes[index],\n                volume: volumes[index]\n            };\n            let result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n    ;\n}\nOBV.calculate = obv;\nexport function obv(input) {\n    Indicator.reverseInputs(input);\n    var result = new OBV(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/9/16.\n */\n\"use strict\";\nimport { ROC } from './ROC.js';\nimport { EMA } from '../moving_averages/EMA.js';\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class TRIXInput extends IndicatorInput {\n}\n;\nexport class TRIX extends Indicator {\n    constructor(input) {\n        super(input);\n        let priceArray = input.values;\n        let period = input.period;\n        let format = this.format;\n        let ema = new EMA({ period: period, values: [], format: (v) => { return v; } });\n        let emaOfema = new EMA({ period: period, values: [], format: (v) => { return v; } });\n        let emaOfemaOfema = new EMA({ period: period, values: [], format: (v) => { return v; } });\n        let trixROC = new ROC({ period: 1, values: [], format: (v) => { return v; } });\n        this.result = [];\n        this.generator = (function* () {\n            let tick = yield;\n            while (true) {\n                let initialema = ema.nextValue(tick);\n                let smoothedResult = initialema ? emaOfema.nextValue(initialema) : undefined;\n                let doubleSmoothedResult = smoothedResult ? emaOfemaOfema.nextValue(smoothedResult) : undefined;\n                let result = doubleSmoothedResult ? trixROC.nextValue(doubleSmoothedResult) : undefined;\n                tick = yield result ? format(result) : undefined;\n            }\n        })();\n        this.generator.next();\n        priceArray.forEach((tick) => {\n            let result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        let nextResult = this.generator.next(price);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nTRIX.calculate = trix;\nexport function trix(input) {\n    Indicator.reverseInputs(input);\n    var result = new TRIX(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { EMA } from '../moving_averages/EMA';\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class ForceIndexInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.period = 1;\n    }\n}\n;\nexport class ForceIndex extends Indicator {\n    constructor(input) {\n        super(input);\n        var closes = input.close;\n        var volumes = input.volume;\n        var period = input.period || 1;\n        if (!((volumes.length === closes.length))) {\n            throw ('Inputs(volume, close) not of equal size');\n        }\n        let emaForceIndex = new EMA({ values: [], period: period });\n        this.result = [];\n        this.generator = (function* () {\n            var previousTick = yield;\n            var tick = yield;\n            let forceIndex;\n            while (true) {\n                forceIndex = (tick.close - previousTick.close) * tick.volume;\n                previousTick = tick;\n                tick = yield emaForceIndex.nextValue(forceIndex);\n            }\n        })();\n        this.generator.next();\n        volumes.forEach((tick, index) => {\n            var result = this.generator.next({\n                close: closes[index],\n                volume: volumes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined) {\n            return result;\n        }\n    }\n    ;\n}\nForceIndex.calculate = forceindex;\nexport function forceindex(input) {\n    Indicator.reverseInputs(input);\n    var result = new ForceIndex(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class CCIInput extends IndicatorInput {\n}\n;\nexport class CCI extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var period = input.period;\n        var format = this.format;\n        let constant = .015;\n        var currentTpSet = new LinkedList(period);\n        ;\n        var tpSMACalculator = new SMA({ period: period, values: [], format: (v) => { return v; } });\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var tick = yield;\n            while (true) {\n                let tp = (tick.high + tick.low + tick.close) / 3;\n                currentTpSet.push(tp);\n                let smaTp = tpSMACalculator.nextValue(tp);\n                let meanDeviation = null;\n                let cci;\n                let sum = 0;\n                if (smaTp != undefined) {\n                    //First, subtract the most recent 20-period average of the typical price from each period's typical price. \n                    //Second, take the absolute values of these numbers.\n                    //Third,sum the absolute values. \n                    for (let x of currentTpSet.iterator()) {\n                        sum = sum + (Math.abs(x - smaTp));\n                    }\n                    //Fourth, divide by the total number of periods (20). \n                    meanDeviation = sum / period;\n                    cci = (tp - smaTp) / (constant * meanDeviation);\n                }\n                tick = yield cci;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined) {\n            return result;\n        }\n    }\n    ;\n}\nCCI.calculate = cci;\nexport function cci(input) {\n    Indicator.reverseInputs(input);\n    var result = new CCI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nexport class AwesomeOscillatorInput extends IndicatorInput {\n}\nexport class AwesomeOscillator extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var fastPeriod = input.fastPeriod;\n        var slowPeriod = input.slowPeriod;\n        var slowSMA = new SMA({ values: [], period: slowPeriod });\n        var fastSMA = new SMA({ values: [], period: fastPeriod });\n        this.result = [];\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var medianPrice;\n            var slowSmaValue;\n            var fastSmaValue;\n            tick = yield;\n            while (true) {\n                medianPrice = (tick.high + tick.low) / 2;\n                slowSmaValue = slowSMA.nextValue(medianPrice);\n                fastSmaValue = fastSMA.nextValue(medianPrice);\n                if (slowSmaValue !== undefined && fastSmaValue !== undefined) {\n                    result = fastSmaValue - slowSmaValue;\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(this.format(result.value));\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return this.format(result.value);\n        }\n    }\n    ;\n}\nAwesomeOscillator.calculate = awesomeoscillator;\nexport function awesomeoscillator(input) {\n    Indicator.reverseInputs(input);\n    var result = new AwesomeOscillator(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class VWAPInput extends IndicatorInput {\n}\n;\nexport class VWAP extends Indicator {\n    constructor(input) {\n        super(input);\n        var lows = input.low;\n        var highs = input.high;\n        var closes = input.close;\n        var volumes = input.volume;\n        var format = this.format;\n        if (!((lows.length === highs.length) && (highs.length === closes.length))) {\n            throw ('Inputs(low,high, close) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var tick = yield;\n            let cumulativeTotal = 0;\n            let cumulativeVolume = 0;\n            while (true) {\n                let typicalPrice = (tick.high + tick.low + tick.close) / 3;\n                let total = tick.volume * typicalPrice;\n                cumulativeTotal = cumulativeTotal + total;\n                cumulativeVolume = cumulativeVolume + tick.volume;\n                tick = yield cumulativeTotal / cumulativeVolume;\n                ;\n            }\n        })();\n        this.generator.next();\n        lows.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: highs[index],\n                low: lows[index],\n                close: closes[index],\n                volume: volumes[index]\n            });\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    ;\n    nextValue(price) {\n        let result = this.generator.next(price).value;\n        if (result != undefined) {\n            return result;\n        }\n    }\n    ;\n}\nVWAP.calculate = vwap;\nexport function vwap(input) {\n    Indicator.reverseInputs(input);\n    var result = new VWAP(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class VolumeProfileInput extends IndicatorInput {\n}\nexport class VolumeProfileOutput {\n}\nexport function priceFallsBetweenBarRange(low, high, low1, high1) {\n    return (low <= low1 && high >= low1) || (low1 <= low && high1 >= low);\n}\nexport class VolumeProfile extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        var opens = input.open;\n        var volumes = input.volume;\n        var bars = input.noOfBars;\n        if (!((lows.length === highs.length) && (highs.length === closes.length) && (highs.length === volumes.length))) {\n            throw ('Inputs(low,high, close, volumes) not of equal size');\n        }\n        this.result = [];\n        var max = Math.max(...highs, ...lows, ...closes, ...opens);\n        var min = Math.min(...highs, ...lows, ...closes, ...opens);\n        var barRange = (max - min) / bars;\n        var lastEnd = min;\n        for (let i = 0; i < bars; i++) {\n            let rangeStart = lastEnd;\n            let rangeEnd = rangeStart + barRange;\n            lastEnd = rangeEnd;\n            let bullishVolume = 0;\n            let bearishVolume = 0;\n            let totalVolume = 0;\n            for (let priceBar = 0; priceBar < highs.length; priceBar++) {\n                let priceBarStart = lows[priceBar];\n                let priceBarEnd = highs[priceBar];\n                let priceBarOpen = opens[priceBar];\n                let priceBarClose = closes[priceBar];\n                let priceBarVolume = volumes[priceBar];\n                if (priceFallsBetweenBarRange(rangeStart, rangeEnd, priceBarStart, priceBarEnd)) {\n                    totalVolume = totalVolume + priceBarVolume;\n                    if (priceBarOpen > priceBarClose) {\n                        bearishVolume = bearishVolume + priceBarVolume;\n                    }\n                    else {\n                        bullishVolume = bullishVolume + priceBarVolume;\n                    }\n                }\n            }\n            this.result.push({\n                rangeStart, rangeEnd, bullishVolume, bearishVolume, totalVolume\n            });\n        }\n    }\n    ;\n    nextValue(price) {\n        throw ('Next value not supported for volume profile');\n    }\n    ;\n}\nVolumeProfile.calculate = volumeprofile;\nexport function volumeprofile(input) {\n    Indicator.reverseInputs(input);\n    var result = new VolumeProfile(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class TypicalPriceInput extends IndicatorInput {\n}\nexport class TypicalPrice extends Indicator {\n    constructor(input) {\n        super(input);\n        this.result = [];\n        this.generator = (function* () {\n            let priceInput = yield;\n            while (true) {\n                priceInput = yield (priceInput.high + priceInput.low + priceInput.close) / 3;\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: input.high[index],\n                low: input.low[index],\n                close: input.close[index],\n            });\n            this.result.push(result.value);\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        return result;\n    }\n    ;\n}\nTypicalPrice.calculate = typicalprice;\nexport function typicalprice(input) {\n    Indicator.reverseInputs(input);\n    var result = new TypicalPrice(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/17/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { TypicalPrice } from '../chart_types/TypicalPrice';\nimport FixedSizeLinkedList from '../Utils/FixedSizeLinkedList';\nexport class MFIInput extends IndicatorInput {\n}\nexport class MFI extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        var volumes = input.volume;\n        var period = input.period;\n        var typicalPrice = new TypicalPrice({ low: [], high: [], close: [] });\n        var positiveFlow = new FixedSizeLinkedList(period, false, false, true);\n        var negativeFlow = new FixedSizeLinkedList(period, false, false, true);\n        if (!((lows.length === highs.length) && (highs.length === closes.length) && (highs.length === volumes.length))) {\n            throw ('Inputs(low,high, close, volumes) not of equal size');\n        }\n        this.result = [];\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var lastClose;\n            var positiveFlowForPeriod;\n            var rawMoneyFlow = 0;\n            var moneyFlowRatio;\n            var negativeFlowForPeriod;\n            let typicalPriceValue = null;\n            let prevousTypicalPrice = null;\n            tick = yield;\n            lastClose = tick.close; //Fist value \n            tick = yield;\n            while (true) {\n                var { high, low, close, volume } = tick;\n                var positionMoney = 0;\n                var negativeMoney = 0;\n                typicalPriceValue = typicalPrice.nextValue({ high, low, close });\n                rawMoneyFlow = typicalPriceValue * volume;\n                if ((typicalPriceValue != null) && (prevousTypicalPrice != null)) {\n                    typicalPriceValue > prevousTypicalPrice ? positionMoney = rawMoneyFlow : negativeMoney = rawMoneyFlow;\n                    positiveFlow.push(positionMoney);\n                    negativeFlow.push(negativeMoney);\n                    positiveFlowForPeriod = positiveFlow.periodSum;\n                    negativeFlowForPeriod = negativeFlow.periodSum;\n                    if ((positiveFlow.totalPushed >= period) && (positiveFlow.totalPushed >= period)) {\n                        moneyFlowRatio = positiveFlowForPeriod / negativeFlowForPeriod;\n                        result = 100 - 100 / (1 + moneyFlowRatio);\n                    }\n                }\n                prevousTypicalPrice = typicalPriceValue;\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n                close: closes[index],\n                volume: volumes[index]\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(parseFloat(result.value.toFixed(2)));\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return (parseFloat(result.value.toFixed(2)));\n        }\n    }\n    ;\n}\nMFI.calculate = mfi;\nexport function mfi(input) {\n    Indicator.reverseInputs(input);\n    var result = new MFI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { IndicatorInput, Indicator } from '../indicator/indicator';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/10/16.\n */\n\"use strict\";\nimport { SMA } from '../moving_averages/SMA';\nimport { RSI } from '../oscillators/RSI';\nimport { Stochastic } from '../momentum/Stochastic';\nexport class StochasticRsiInput extends IndicatorInput {\n}\n;\nexport class StochasticRSIOutput {\n}\n;\nexport class StochasticRSI extends Indicator {\n    constructor(input) {\n        super(input);\n        let closes = input.values;\n        let rsiPeriod = input.rsiPeriod;\n        let stochasticPeriod = input.stochasticPeriod;\n        let kPeriod = input.kPeriod;\n        let dPeriod = input.dPeriod;\n        let format = this.format;\n        this.result = [];\n        this.generator = (function* () {\n            let index = 1;\n            let rsi = new RSI({ period: rsiPeriod, values: [] });\n            let stochastic = new Stochastic({ period: stochasticPeriod, high: [], low: [], close: [], signalPeriod: kPeriod });\n            let dSma = new SMA({\n                period: dPeriod,\n                values: [],\n                format: (v) => { return v; }\n            });\n            let lastRSI, stochasticRSI, d, result;\n            var tick = yield;\n            while (true) {\n                lastRSI = rsi.nextValue(tick);\n                if (lastRSI !== undefined) {\n                    var stochasticInput = { high: lastRSI, low: lastRSI, close: lastRSI };\n                    stochasticRSI = stochastic.nextValue(stochasticInput);\n                    if (stochasticRSI !== undefined && stochasticRSI.d !== undefined) {\n                        d = dSma.nextValue(stochasticRSI.d);\n                        if (d !== undefined)\n                            result = {\n                                stochRSI: stochasticRSI.k,\n                                k: stochasticRSI.d,\n                                d: d\n                            };\n                    }\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        closes.forEach((tick, index) => {\n            var result = this.generator.next(tick);\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(input) {\n        let nextResult = this.generator.next(input);\n        if (nextResult.value !== undefined)\n            return nextResult.value;\n    }\n    ;\n}\nStochasticRSI.calculate = stochasticrsi;\nexport function stochasticrsi(input) {\n    Indicator.reverseInputs(input);\n    var result = new StochasticRSI(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport FixedSizedLinkedList from './FixedSizeLinkedList';\nexport class HighestInput extends IndicatorInput {\n}\nexport class Highest extends Indicator {\n    constructor(input) {\n        super(input);\n        var values = input.values;\n        var period = input.period;\n        this.result = [];\n        var periodList = new FixedSizedLinkedList(period, true, false, false);\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var high;\n            tick = yield;\n            while (true) {\n                periodList.push(tick);\n                if (periodList.totalPushed >= period) {\n                    high = periodList.periodHigh;\n                }\n                tick = yield high;\n            }\n        })();\n        this.generator.next();\n        values.forEach((value, index) => {\n            var result = this.generator.next(value);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nHighest.calculate = highest;\nexport function highest(input) {\n    Indicator.reverseInputs(input);\n    var result = new Highest(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport FixedSizedLinkedList from './FixedSizeLinkedList';\nexport class LowestInput extends IndicatorInput {\n}\nexport class Lowest extends Indicator {\n    constructor(input) {\n        super(input);\n        var values = input.values;\n        var period = input.period;\n        this.result = [];\n        var periodList = new FixedSizedLinkedList(period, false, true, false);\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var high;\n            tick = yield;\n            while (true) {\n                periodList.push(tick);\n                if (periodList.totalPushed >= period) {\n                    high = periodList.periodLow;\n                }\n                tick = yield high;\n            }\n        })();\n        this.generator.next();\n        values.forEach((value, index) => {\n            var result = this.generator.next(value);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nLowest.calculate = lowest;\nexport function lowest(input) {\n    Indicator.reverseInputs(input);\n    var result = new Lowest(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport FixedSizedLinkedList from './FixedSizeLinkedList';\nexport class SumInput extends IndicatorInput {\n}\nexport class Sum extends Indicator {\n    constructor(input) {\n        super(input);\n        var values = input.values;\n        var period = input.period;\n        this.result = [];\n        var periodList = new FixedSizedLinkedList(period, false, false, true);\n        this.generator = (function* () {\n            var result;\n            var tick;\n            var high;\n            tick = yield;\n            while (true) {\n                periodList.push(tick);\n                if (periodList.totalPushed >= period) {\n                    high = periodList.periodSum;\n                }\n                tick = yield high;\n            }\n        })();\n        this.generator.next();\n        values.forEach((value, index) => {\n            var result = this.generator.next(value);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nSum.calculate = sum;\nexport function sum(input) {\n    Indicator.reverseInputs(input);\n    var result = new Sum(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { CandleList } from '../StockData';\nimport { atr } from '../directionalmovement/ATR';\n/**\n * Created by AAravindan on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class RenkoInput extends IndicatorInput {\n}\nclass <PERSON><PERSON> extends Indicator {\n    constructor(input) {\n        super(input);\n        var format = this.format;\n        let useATR = input.useATR;\n        let brickSize = input.brickSize || 0;\n        if (useATR) {\n            let atrResult = atr(Object.assign({}, input));\n            brickSize = atrResult[atrResult.length - 1];\n        }\n        this.result = new CandleList();\n        ;\n        if (brickSize === 0) {\n            console.error('Not enough data to calculate brickSize for renko when using ATR');\n            return;\n        }\n        let lastOpen = 0;\n        let lastHigh = 0;\n        let lastLow = Infinity;\n        let lastClose = 0;\n        let lastVolume = 0;\n        let lastTimestamp = 0;\n        this.generator = (function* () {\n            let candleData = yield;\n            while (true) {\n                //Calculating first bar\n                if (lastOpen === 0) {\n                    lastOpen = candleData.close;\n                    lastHigh = candleData.high;\n                    lastLow = candleData.low;\n                    lastClose = candleData.close;\n                    lastVolume = candleData.volume;\n                    lastTimestamp = candleData.timestamp;\n                    candleData = yield;\n                    continue;\n                }\n                let absoluteMovementFromClose = Math.abs(candleData.close - lastClose);\n                let absoluteMovementFromOpen = Math.abs(candleData.close - lastOpen);\n                if ((absoluteMovementFromClose >= brickSize) && (absoluteMovementFromOpen >= brickSize)) {\n                    let reference = absoluteMovementFromClose > absoluteMovementFromOpen ? lastOpen : lastClose;\n                    let calculated = {\n                        open: reference,\n                        high: lastHigh > candleData.high ? lastHigh : candleData.high,\n                        low: lastLow < candleData.Low ? lastLow : candleData.low,\n                        close: reference > candleData.close ? (reference - brickSize) : (reference + brickSize),\n                        volume: lastVolume + candleData.volume,\n                        timestamp: candleData.timestamp\n                    };\n                    lastOpen = calculated.open;\n                    lastHigh = calculated.close;\n                    lastLow = calculated.close;\n                    lastClose = calculated.close;\n                    lastVolume = 0;\n                    candleData = yield calculated;\n                }\n                else {\n                    lastHigh = lastHigh > candleData.high ? lastHigh : candleData.high;\n                    lastLow = lastLow < candleData.Low ? lastLow : candleData.low;\n                    lastVolume = lastVolume + candleData.volume;\n                    lastTimestamp = candleData.timestamp;\n                    candleData = yield;\n                }\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                open: input.open[index],\n                high: input.high[index],\n                low: input.low[index],\n                close: input.close[index],\n                volume: input.volume[index],\n                timestamp: input.timestamp[index]\n            });\n            if (result.value) {\n                this.result.open.push(result.value.open);\n                this.result.high.push(result.value.high);\n                this.result.low.push(result.value.low);\n                this.result.close.push(result.value.close);\n                this.result.volume.push(result.value.volume);\n                this.result.timestamp.push(result.value.timestamp);\n            }\n        });\n    }\n    nextValue(price) {\n        console.error('Cannot calculate next value on Renko, Every value has to be recomputed for every change, use calcualte method');\n        return null;\n    }\n    ;\n}\nRenko.calculate = renko;\nexport function renko(input) {\n    Indicator.reverseInputs(input);\n    var result = new Renko(input).result;\n    if (input.reversedInput) {\n        result.open.reverse();\n        result.high.reverse();\n        result.low.reverse();\n        result.close.reverse();\n        result.volume.reverse();\n        result.timestamp.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { CandleList } from '../StockData';\n/**\n * Created by <PERSON><PERSON><PERSON><PERSON> on 5/4/16.\n */\nimport { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class HeikinAshiInput extends IndicatorInput {\n}\nexport class <PERSON>ikin<PERSON><PERSON> extends Indicator {\n    constructor(input) {\n        super(input);\n        var format = this.format;\n        this.result = new CandleList();\n        let lastOpen = null;\n        let lastHigh = 0;\n        let lastLow = Infinity;\n        let lastClose = 0;\n        let lastVolume = 0;\n        let lastTimestamp = 0;\n        this.generator = (function* () {\n            let candleData = yield;\n            let calculated = null;\n            while (true) {\n                if (lastOpen === null) {\n                    lastOpen = (candleData.close + candleData.open) / 2;\n                    lastHigh = candleData.high;\n                    lastLow = candleData.low;\n                    lastClose = (candleData.close + candleData.open + candleData.high + candleData.low) / 4;\n                    lastVolume = (candleData.volume || 0);\n                    lastTimestamp = (candleData.timestamp || 0);\n                    calculated = {\n                        open: lastOpen,\n                        high: lastHigh,\n                        low: lastLow,\n                        close: lastClose,\n                        volume: candleData.volume || 0,\n                        timestamp: (candleData.timestamp || 0)\n                    };\n                }\n                else {\n                    let newClose = (candleData.close + candleData.open + candleData.high + candleData.low) / 4;\n                    let newOpen = (lastOpen + lastClose) / 2;\n                    let newHigh = Math.max(newOpen, newClose, candleData.high);\n                    let newLow = Math.min(candleData.low, newOpen, newClose);\n                    calculated = {\n                        close: newClose,\n                        open: newOpen,\n                        high: newHigh,\n                        low: newLow,\n                        volume: (candleData.volume || 0),\n                        timestamp: (candleData.timestamp || 0)\n                    };\n                    lastClose = newClose;\n                    lastOpen = newOpen;\n                    lastHigh = newHigh;\n                    lastLow = newLow;\n                }\n                candleData = yield calculated;\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                open: input.open[index],\n                high: input.high[index],\n                low: input.low[index],\n                close: input.close[index],\n                volume: input.volume ? input.volume[index] : input.volume,\n                timestamp: input.timestamp ? input.timestamp[index] : input.timestamp\n            });\n            if (result.value) {\n                this.result.open.push(result.value.open);\n                this.result.high.push(result.value.high);\n                this.result.low.push(result.value.low);\n                this.result.close.push(result.value.close);\n                this.result.volume.push(result.value.volume);\n                this.result.timestamp.push(result.value.timestamp);\n            }\n        });\n    }\n    nextValue(price) {\n        var result = this.generator.next(price).value;\n        return result;\n    }\n    ;\n}\nHeikinAshi.calculate = heikinashi;\nexport function heikinashi(input) {\n    Indicator.reverseInputs(input);\n    var result = new HeikinAshi(input).result;\n    if (input.reversedInput) {\n        result.open.reverse();\n        result.high.reverse();\n        result.low.reverse();\n        result.close.reverse();\n        result.volume.reverse();\n        result.timestamp.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "export default class CandlestickFinder {\n    constructor() {\n        // if (new.target === Abstract) {\n        //     throw new TypeError(\"Abstract class\");\n        // }\n    }\n    approximateEqual(a, b) {\n        let left = parseFloat(Math.abs(a - b).toPrecision(4)) * 1;\n        let right = parseFloat((a * 0.001).toPrecision(4)) * 1;\n        return left <= right;\n    }\n    logic(data) {\n        throw \"this has to be implemented\";\n    }\n    getAllPatternIndex(data) {\n        if (data.close.length < this.requiredCount) {\n            console.warn('Data count less than data required for the strategy ', this.name);\n            return [];\n        }\n        if (data.reversedInput) {\n            data.open.reverse();\n            data.high.reverse();\n            data.low.reverse();\n            data.close.reverse();\n        }\n        let strategyFn = this.logic;\n        return this._generateDataForCandleStick(data)\n            .map((current, index) => {\n            return strategyFn.call(this, current) ? index : undefined;\n        }).filter((hasIndex) => {\n            return hasIndex;\n        });\n    }\n    hasPattern(data) {\n        if (data.close.length < this.requiredCount) {\n            console.warn('Data count less than data required for the strategy ', this.name);\n            return false;\n        }\n        if (data.reversedInput) {\n            data.open.reverse();\n            data.high.reverse();\n            data.low.reverse();\n            data.close.reverse();\n        }\n        let strategyFn = this.logic;\n        return strategyFn.call(this, this._getLastDataForCandleStick(data));\n    }\n    _getLastDataForCandleStick(data) {\n        let requiredCount = this.requiredCount;\n        if (data.close.length === requiredCount) {\n            return data;\n        }\n        else {\n            let returnVal = {\n                open: [],\n                high: [],\n                low: [],\n                close: []\n            };\n            let i = 0;\n            let index = data.close.length - requiredCount;\n            while (i < requiredCount) {\n                returnVal.open.push(data.open[index + i]);\n                returnVal.high.push(data.high[index + i]);\n                returnVal.low.push(data.low[index + i]);\n                returnVal.close.push(data.close[index + i]);\n                i++;\n            }\n            return returnVal;\n        }\n    }\n    _generateDataForCandleStick(data) {\n        let requiredCount = this.requiredCount;\n        let generatedData = data.close.map(function (currentData, index) {\n            let i = 0;\n            let returnVal = {\n                open: [],\n                high: [],\n                low: [],\n                close: []\n            };\n            while (i < requiredCount) {\n                returnVal.open.push(data.open[index + i]);\n                returnVal.high.push(data.high[index + i]);\n                returnVal.low.push(data.low[index + i]);\n                returnVal.close.push(data.close[index + i]);\n                i++;\n            }\n            return returnVal;\n        }).filter((val, index) => { return (index <= (data.close.length - requiredCount)); });\n        return generatedData;\n    }\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class MorningStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'MorningStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let isSmallBodyExists = ((firstdaysLow > seconddaysLow) &&\n            (firstdaysLow > seconddaysHigh));\n        let isThirdBullish = thirddaysOpen < thirddaysClose;\n        let gapExists = ((seconddaysHigh < firstdaysLow) &&\n            (seconddaysLow < firstdaysLow) &&\n            (thirddaysOpen > seconddaysHigh) &&\n            (seconddaysClose < thirddaysOpen));\n        let doesCloseAboveFirstMidpoint = thirddaysClose > firstdaysMidpoint;\n        return (isFirstBearish && isSmallBodyExists && gapExists && isThirdBullish && doesCloseAboveFirstMidpoint);\n    }\n}\nexport function morningstar(data) {\n    return new MorningStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishEngulfingPattern extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishEngulfingPattern';\n        this.requiredCount = 2;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBullishEngulfing = ((firstdaysClose < firstdaysOpen) &&\n            (firstdaysOpen > seconddaysOpen) &&\n            (firstdaysClose > seconddaysOpen) &&\n            (firstdaysOpen < seconddaysClose));\n        return (isBullishEngulfing);\n    }\n}\nexport function bullishengulfingpattern(data) {\n    return new BullishEngulfingPattern().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishHarami extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = \"<PERSON>ishH<PERSON><PERSON>\";\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBullishHaramiPattern = ((firstdaysOpen > seconddaysOpen) &&\n            (firstdaysClose < seconddaysOpen) &&\n            (firstdaysClose < seconddaysClose) &&\n            (firstdaysOpen > seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        return (isBullishHaramiPattern);\n    }\n}\nexport function bullishharami(data) {\n    return new BullishHarami().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishHaramiCross extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = 'BullishHaramiCross';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBullishHaramiCrossPattern = ((firstdaysOpen > seconddaysOpen) &&\n            (firstdaysClose < seconddaysOpen) &&\n            (firstdaysClose < seconddaysClose) &&\n            (firstdaysOpen > seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        let isSecondDayDoji = this.approximateEqual(seconddaysOpen, seconddaysClose);\n        return (isBullishHaramiCrossPattern && isSecondDayDoji);\n    }\n}\nexport function bullishharamicross(data) {\n    return new BullishHaramiCross().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class <PERSON>ji extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = '<PERSON><PERSON>';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isOpenEqualsClose = this.approximateEqual(daysOpen, daysClose);\n        let isHighEqualsOpen = isOpenEqualsClose && this.approximateEqual(daysOpen, daysHigh);\n        let isLowEqualsClose = isOpenEqualsClose && this.approximateEqual(daysClose, daysLow);\n        return (isOpenEqualsClose && isHighEqualsOpen == isLowEqualsClose);\n    }\n}\nexport function doji(data) {\n    return new Doji().hasPattern(data);\n}\n", "import Doji from './Doji';\nimport CandlestickFinder from './CandlestickFinder';\nexport default class MorningDojiStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'MorningDojiStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let dojiExists = new Doji().hasPattern({\n            \"open\": [seconddaysOpen],\n            \"close\": [seconddaysClose],\n            \"high\": [seconddaysHigh],\n            \"low\": [seconddaysLow]\n        });\n        let isThirdBullish = thirddaysOpen < thirddaysClose;\n        let gapExists = ((seconddaysHigh < firstdaysLow) &&\n            (seconddaysLow < firstdaysLow) &&\n            (thirddaysOpen > seconddaysHigh) &&\n            (seconddaysClose < thirddaysOpen));\n        let doesCloseAboveFirstMidpoint = thirddaysClose > firstdaysMidpoint;\n        return (isFirstBearish && dojiExists && isThirdBullish && gapExists &&\n            doesCloseAboveFirstMidpoint);\n    }\n}\nexport function morningdojistar(data) {\n    return new MorningDojiStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class DownsideTasukiGap extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 3;\n        this.name = 'DownsideTasukiGap';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let isSecondBearish = seconddaysClose < seconddaysOpen;\n        let isThirdBullish = thirddaysClose > thirddaysOpen;\n        let isFirstGapExists = seconddaysHigh < firstdaysLow;\n        let isDownsideTasukiGap = ((seconddaysOpen > thirddaysOpen) &&\n            (seconddaysClose < thirddaysOpen) &&\n            (thirddaysClose > seconddaysOpen) &&\n            (thirddaysClose < firstdaysClose));\n        return (isFirstBearish && isSecondBearish && isThirdBullish && isFirstGapExists && isDownsideTasukiGap);\n    }\n}\nexport function downsidetasukigap(data) {\n    return new DownsideTasukiGap().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishMarubozu extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishMarubozu';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBullishMarbozu = this.approximateEqual(daysClose, daysHigh) &&\n            this.approximateEqual(daysLow, daysOpen) &&\n            daysOpen < daysClose &&\n            daysOpen < daysHigh;\n        return (isBullishMarbozu);\n    }\n}\nexport function bullishmarubozu(data) {\n    return new BullishMarubozu().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class PiercingLine extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = 'PiercingLine';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isDowntrend = seconddaysLow < firstdaysLow;\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let isSecondBullish = seconddaysClose > seconddaysOpen;\n        let isPiercingLinePattern = ((firstdaysLow > seconddaysOpen) &&\n            (seconddaysClose > firstdaysMidpoint));\n        return (isDowntrend && isFirstBearish && isPiercingLinePattern && isSecondBullish);\n    }\n}\nexport function piercingline(data) {\n    return new PiercingLine().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class ThreeWhiteSoldiers extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'ThreeWhiteSoldiers';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isUpTrend = seconddaysHigh > firstdaysHigh &&\n            thirddaysHigh > seconddaysHigh;\n        let isAllBullish = firstdaysOpen < firstdaysClose &&\n            seconddaysOpen < seconddaysClose &&\n            thirddaysOpen < thirddaysClose;\n        let doesOpenWithinPreviousBody = firstdaysClose > seconddaysOpen &&\n            seconddaysOpen < firstdaysHigh &&\n            seconddaysHigh > thirddaysOpen &&\n            thirddaysOpen < seconddaysClose;\n        return (isUpTrend && isAllBullish && doesOpenWithinPreviousBody);\n    }\n}\nexport function threewhitesoldiers(data) {\n    return new ThreeWhiteSoldiers().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBullishHammer = daysClose > daysOpen;\n        isBullishHammer = isBullishHammer && this.approximateEqual(daysClose, daysHigh);\n        isBullishHammer = isBullishHammer && (daysClose - daysOpen) <= 2 * (daysOpen - daysLow);\n        return isBullishHammer;\n    }\n}\nexport function bullishhammerstick(data) {\n    return new BullishHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishInvertedHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishInvertedHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBullishInvertedHammer = daysClose > daysOpen;\n        isBullishInvertedHammer = isBullishInvertedHammer && this.approximateEqual(daysOpen, daysLow);\n        isBullishInvertedHammer = isBullishInvertedHammer && (daysClose - daysOpen) <= 2 * (daysHigh - daysClose);\n        return isBullishInvertedHammer;\n    }\n}\nexport function bullishinvertedhammerstick(data) {\n    return new BullishInvertedHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBearishHammer = daysOpen > daysClose;\n        isBearishHammer = isBearishHammer && this.approximateEqual(daysOpen, daysHigh);\n        isBearishHammer = isBearishHammer && (daysOpen - daysClose) <= 2 * (daysClose - daysLow);\n        return isBearishHammer;\n    }\n}\nexport function bearishhammerstick(data) {\n    return new BearishHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishInvertedHammerStick extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishInvertedHammerStick';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBearishInvertedHammer = daysOpen > daysClose;\n        isBearishInvertedHammer = isBearishInvertedHammer && this.approximateEqual(daysClose, daysLow);\n        isBearishInvertedHammer = isBearishInvertedHammer && (daysOpen - daysClose) <= 2 * (daysHigh - daysOpen);\n        return isBearishInvertedHammer;\n    }\n}\nexport function bearishinvertedhammerstick(data) {\n    return new BearishInvertedHammerStick().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nimport { bearishhammerstick } from './BearishHammerStick';\nimport { bearishinvertedhammerstick } from './BearishInvertedHammerStick';\nimport { bullishhammerstick } from './BullishHammerStick';\nimport { bullishinvertedhammerstick } from './BullishInvertedHammerStick';\nexport default class HammerPattern extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'HammerPattern';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        let isPattern = this.downwardTrend(data);\n        isPattern = isPattern && this.includesHammer(data);\n        isPattern = isPattern && this.hasConfirmation(data);\n        return isPattern;\n    }\n    downwardTrend(data, confirm = true) {\n        let end = confirm ? 3 : 4;\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, end), period: end - 1 });\n        let losses = averageloss({ values: data.close.slice(0, end), period: end - 1 });\n        // Downward trend, so more losses than gains\n        return losses > gains;\n    }\n    includesHammer(data, confirm = true) {\n        let start = confirm ? 3 : 4;\n        let end = confirm ? 4 : undefined;\n        let possibleHammerData = {\n            open: data.open.slice(start, end),\n            close: data.close.slice(start, end),\n            low: data.low.slice(start, end),\n            high: data.high.slice(start, end),\n        };\n        let isPattern = bearishhammerstick(possibleHammerData);\n        isPattern = isPattern || bearishinvertedhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishinvertedhammerstick(possibleHammerData);\n        return isPattern;\n    }\n    hasConfirmation(data) {\n        let possibleHammer = {\n            open: data.open[3],\n            close: data.close[3],\n            low: data.low[3],\n            high: data.high[3],\n        };\n        let possibleConfirmation = {\n            open: data.open[4],\n            close: data.close[4],\n            low: data.low[4],\n            high: data.high[4],\n        };\n        // Confirmation candlestick is bullish\n        let isPattern = possibleConfirmation.open < possibleConfirmation.close;\n        return isPattern && possibleHammer.close < possibleConfirmation.close;\n    }\n}\nexport function hammerpattern(data) {\n    return new HammerPattern().hasPattern(data);\n}\n", "import HammerPattern from './HammerPattern';\nexport default class HammerPatternUnconfirmed extends HammerPattern {\n    constructor() {\n        super();\n        this.name = 'HammerPatternUnconfirmed';\n    }\n    logic(data) {\n        let isPattern = this.downwardTrend(data, false);\n        isPattern = isPattern && this.includesHammer(data, false);\n        return isPattern;\n    }\n}\nexport function hammerpatternunconfirmed(data) {\n    return new HammerPatternUnconfirmed().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nexport default class TweezerBottom extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'TweezerBottom';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        return this.downwardTrend(data) && data.low[3] == data.low[4];\n    }\n    downwardTrend(data) {\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, 3), period: 2 });\n        let losses = averageloss({ values: data.close.slice(0, 3), period: 2 });\n        // Downward trend, so more losses than gains\n        return losses > gains;\n    }\n}\nexport function tweezerbottom(data) {\n    return new TweezerBottom().hasPattern(data);\n}\n", "import MorningStar from './MorningStar';\nimport BullishEngulfingPattern from './BullishEngulfingPattern';\nimport <PERSON><PERSON><PERSON><PERSON><PERSON> from './BullishHarami';\nimport BullishHaramiCross from './BullishHaramiCross';\nimport MorningDojiStar from './MorningDojiStar';\nimport DownsideTasukiGap from './DownsideTasukiGap';\nimport BullishMarubozu from './BullishMarubozu';\nimport PiercingLine from './PiercingLine';\nimport ThreeWhiteSoldiers from './ThreeWhiteSoldiers';\nimport BullishHammerStick from './BullishHammerStick';\nimport BullishInvertedHammerStick from './BullishInvertedHammerStick';\nimport HammerPattern from './HammerPattern';\nimport HammerPatternUnconfirmed from './HammerPatternUnconfirmed';\nimport CandlestickFinder from './CandlestickFinder';\nimport TweezerBottom from './TweezerBottom';\nlet bullishPatterns = [\n    new BullishEngulfingPattern(),\n    new DownsideTasukiGap(),\n    new BullishHarami(),\n    new BullishHaramiCross(),\n    new MorningDojiStar(),\n    new MorningStar(),\n    new BullishMarubozu(),\n    new PiercingLine(),\n    new ThreeWhiteSoldiers(),\n    new BullishHammerStick(),\n    new BullishInvertedHammerStick(),\n    new HammerPattern(),\n    new HammerPatternUnconfirmed(),\n    new TweezerBottom()\n];\nexport default class BullishPatterns extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'Bullish Candlesticks';\n    }\n    hasPattern(data) {\n        return bullishPatterns.reduce(function (state, pattern) {\n            let result = pattern.hasPattern(data);\n            return state || result;\n        }, false);\n    }\n}\nexport function bullish(data) {\n    return new BullishPatterns().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishEngulfingPattern extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishEngulfingPattern';\n        this.requiredCount = 2;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBearishEngulfing = ((firstdaysClose > firstdaysOpen) &&\n            (firstdaysOpen < seconddaysOpen) &&\n            (firstdaysClose < seconddaysOpen) &&\n            (firstdaysOpen > seconddaysClose));\n        return (isBearishEngulfing);\n    }\n}\nexport function bearishengulfingpattern(data) {\n    return new BearishEngulfingPattern().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishHarami extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = '<PERSON><PERSON><PERSON><PERSON><PERSON>';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBearishHaramiPattern = ((firstdaysOpen < seconddaysOpen) &&\n            (firstdaysClose > seconddaysOpen) &&\n            (firstdaysClose > seconddaysClose) &&\n            (firstdaysOpen < seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        return (isBearishHaramiPattern);\n    }\n}\nexport function bearishharami(data) {\n    return new BearishHarami().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishHaramiCross extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 2;\n        this.name = 'BearishHaramiCross';\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let isBearishHaramiCrossPattern = ((firstdaysOpen < seconddaysOpen) &&\n            (firstdaysClose > seconddaysOpen) &&\n            (firstdaysClose > seconddaysClose) &&\n            (firstdaysOpen < seconddaysLow) &&\n            (firstdaysHigh > seconddaysHigh));\n        let isSecondDayDoji = this.approximateEqual(seconddaysOpen, seconddaysClose);\n        return (isBearishHaramiCrossPattern && isSecondDayDoji);\n    }\n}\nexport function bearishharamicross(data) {\n    return new BearishHaramiCross().hasPattern(data);\n}\n", "import Doji from './Doji';\nimport CandlestickFinder from './CandlestickFinder';\nexport default class EveningDojiStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'EveningDojiStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBullish = firstdaysClose > firstdaysOpen;\n        let dojiExists = new Doji().hasPattern({\n            \"open\": [seconddaysOpen],\n            \"close\": [seconddaysClose],\n            \"high\": [seconddaysHigh],\n            \"low\": [seconddaysLow]\n        });\n        let isThirdBearish = thirddaysOpen > thirddaysClose;\n        let gapExists = ((seconddaysHigh > firstdaysHigh) &&\n            (seconddaysLow > firstdaysHigh) &&\n            (thirddaysOpen < seconddaysLow) &&\n            (seconddaysClose > thirddaysOpen));\n        let doesCloseBelowFirstMidpoint = thirddaysClose < firstdaysMidpoint;\n        return (isFirstBullish && dojiExists && gapExists && isThirdBearish && doesCloseBelowFirstMidpoint);\n    }\n}\nexport function eveningdojistar(data) {\n    return new EveningDojiStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class EveningStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'EveningStar';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let firstdaysMidpoint = ((firstdaysOpen + firstdaysClose) / 2);\n        let isFirstBullish = firstdaysClose > firstdaysOpen;\n        let isSmallBodyExists = ((firstdaysHigh < seconddaysLow) &&\n            (firstdaysHigh < seconddaysHigh));\n        let isThirdBearish = thirddaysOpen > thirddaysClose;\n        let gapExists = ((seconddaysHigh > firstdaysHigh) &&\n            (seconddaysLow > firstdaysHigh) &&\n            (thirddaysOpen < seconddaysLow) &&\n            (seconddaysClose > thirddaysOpen));\n        let doesCloseBelowFirstMidpoint = thirddaysClose < firstdaysMidpoint;\n        return (isFirstBullish && isSmallBodyExists && gapExists && isThirdBearish && doesCloseBelowFirstMidpoint);\n    }\n}\nexport function eveningstar(data) {\n    return new EveningStar().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishMarubozu extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishMarubozu';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isBearishMarbozu = this.approximateEqual(daysOpen, daysHigh) &&\n            this.approximateEqual(daysLow, daysClose) &&\n            daysOpen > daysClose &&\n            daysOpen > daysLow;\n        return (isBearishMarbozu);\n    }\n}\nexport function bearishmarubozu(data) {\n    return new BearishMarubozu().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class ThreeBlackCrows extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'ThreeBlackCrows';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isDownTrend = firstdaysLow > seconddaysLow &&\n            seconddaysLow > thirddaysLow;\n        let isAllBearish = firstdaysOpen > firstdaysClose &&\n            seconddaysOpen > seconddaysClose &&\n            thirddaysOpen > thirddaysClose;\n        let doesOpenWithinPreviousBody = firstdaysOpen > seconddaysOpen &&\n            seconddaysOpen > firstdaysClose &&\n            seconddaysOpen > thirddaysOpen &&\n            thirddaysOpen > seconddaysClose;\n        return (isDownTrend && isAllBearish && doesOpenWithinPreviousBody);\n    }\n}\nexport function threeblackcrows(data) {\n    return new ThreeBlackCrows().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nimport { bearishhammerstick } from './BearishHammerStick';\nimport { bullishhammerstick } from './BullishHammerStick';\nexport default class HangingMan extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'HangingMan';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data);\n        isPattern = isPattern && this.includesHammer(data);\n        isPattern = isPattern && this.hasConfirmation(data);\n        return isPattern;\n    }\n    upwardTrend(data, confirm = true) {\n        let end = confirm ? 3 : 4;\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, end), period: end - 1 });\n        let losses = averageloss({ values: data.close.slice(0, end), period: end - 1 });\n        // Upward trend, so more gains than losses\n        return gains > losses;\n    }\n    includesHammer(data, confirm = true) {\n        let start = confirm ? 3 : 4;\n        let end = confirm ? 4 : undefined;\n        let possibleHammerData = {\n            open: data.open.slice(start, end),\n            close: data.close.slice(start, end),\n            low: data.low.slice(start, end),\n            high: data.high.slice(start, end),\n        };\n        let isPattern = bearishhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishhammerstick(possibleHammerData);\n        return isPattern;\n    }\n    hasConfirmation(data) {\n        let possibleHammer = {\n            open: data.open[3],\n            close: data.close[3],\n            low: data.low[3],\n            high: data.high[3],\n        };\n        let possibleConfirmation = {\n            open: data.open[4],\n            close: data.close[4],\n            low: data.low[4],\n            high: data.high[4],\n        };\n        // Confirmation candlestick is bearish\n        let isPattern = possibleConfirmation.open > possibleConfirmation.close;\n        return isPattern && possibleHammer.close > possibleConfirmation.close;\n    }\n}\nexport function hangingman(data) {\n    return new HangingMan().hasPattern(data);\n}\n", "import HangingMan from './HangingMan';\nexport default class HangingManUnconfirmed extends HangingMan {\n    constructor() {\n        super();\n        this.name = 'HangingManUnconfirmed';\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data, false);\n        isPattern = isPattern && this.includesHammer(data, false);\n        return isPattern;\n    }\n}\nexport function hangingmanunconfirmed(data) {\n    return new HangingManUnconfirmed().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nimport { bearishinvertedhammerstick } from './BearishInvertedHammerStick';\nimport { bullishinvertedhammerstick } from './BullishInvertedHammerStick';\nexport default class ShootingStar extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'ShootingStar';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data);\n        isPattern = isPattern && this.includesHammer(data);\n        isPattern = isPattern && this.hasConfirmation(data);\n        return isPattern;\n    }\n    upwardTrend(data, confirm = true) {\n        let end = confirm ? 3 : 4;\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, end), period: end - 1 });\n        let losses = averageloss({ values: data.close.slice(0, end), period: end - 1 });\n        // Upward trend, so more gains than losses\n        return gains > losses;\n    }\n    includesHammer(data, confirm = true) {\n        let start = confirm ? 3 : 4;\n        let end = confirm ? 4 : undefined;\n        let possibleHammerData = {\n            open: data.open.slice(start, end),\n            close: data.close.slice(start, end),\n            low: data.low.slice(start, end),\n            high: data.high.slice(start, end),\n        };\n        let isPattern = bearishinvertedhammerstick(possibleHammerData);\n        isPattern = isPattern || bullishinvertedhammerstick(possibleHammerData);\n        return isPattern;\n    }\n    hasConfirmation(data) {\n        let possibleHammer = {\n            open: data.open[3],\n            close: data.close[3],\n            low: data.low[3],\n            high: data.high[3],\n        };\n        let possibleConfirmation = {\n            open: data.open[4],\n            close: data.close[4],\n            low: data.low[4],\n            high: data.high[4],\n        };\n        // Confirmation candlestick is bearish\n        let isPattern = possibleConfirmation.open > possibleConfirmation.close;\n        return isPattern && possibleHammer.close > possibleConfirmation.close;\n    }\n}\nexport function shootingstar(data) {\n    return new ShootingStar().hasPattern(data);\n}\n", "import ShootingStar from './ShootingStar';\nexport default class ShootingStarUnconfirmed extends ShootingStar {\n    constructor() {\n        super();\n        this.name = 'ShootingStarUnconfirmed';\n    }\n    logic(data) {\n        let isPattern = this.upwardTrend(data, false);\n        isPattern = isPattern && this.includesHammer(data, false);\n        return isPattern;\n    }\n}\nexport function shootingstarunconfirmed(data) {\n    return new ShootingStarUnconfirmed().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport { averageloss } from '../Utils/AverageLoss';\nimport { averagegain } from '../Utils/AverageGain';\nexport default class TweezerTop extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'TweezerTop';\n        this.requiredCount = 5;\n    }\n    logic(data) {\n        return this.upwardTrend(data) && data.high[3] == data.high[4];\n    }\n    upwardTrend(data) {\n        // Analyze trends in closing prices of the first three or four candlesticks\n        let gains = averagegain({ values: data.close.slice(0, 3), period: 2 });\n        let losses = averageloss({ values: data.close.slice(0, 3), period: 2 });\n        // Upward trend, so more gains than losses\n        return gains > losses;\n    }\n}\nexport function tweezertop(data) {\n    return new TweezerTop().hasPattern(data);\n}\n", "import BearishEngulfingPattern from './BearishEngulfingPattern';\nimport Bearish<PERSON><PERSON><PERSON> from './BearishHarami';\nimport BearishHaramiCross from './BearishHaramiCross';\nimport EveningDojiStar from './EveningDojiStar';\nimport EveningStar from './EveningStar';\nimport BearishMaru<PERSON>zu from './BearishMarubozu';\nimport ThreeBlackCrows from './ThreeBlackCrows';\nimport BearishHammerStick from './BearishHammerStick';\nimport BearishInvertedHammerStick from './BearishInvertedHammerStick';\nimport HangingMan from './HangingMan';\nimport HangingManUnconfirmed from './HangingManUnconfirmed';\nimport ShootingStar from './ShootingStar';\nimport ShootingStarUnconfirmed from './ShootingStarUnconfirmed';\nimport TweezerTop from './TweezerTop';\nimport CandlestickFinder from './CandlestickFinder';\nlet bearishPatterns = [\n    new BearishEngulfingPattern(),\n    new BearishHarami(),\n    new BearishHaramiCross(),\n    new EveningDojiStar(),\n    new EveningStar(),\n    new BearishMarubozu(),\n    new ThreeBlackCrows(),\n    new BearishHammerStick(),\n    new BearishInvertedHammerStick(),\n    new HangingMan(),\n    new HangingManUnconfirmed(),\n    new ShootingStar(),\n    new ShootingStarUnconfirmed(),\n    new TweezerTop()\n];\nexport default class BearishPatterns extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'Bearish Candlesticks';\n    }\n    hasPattern(data) {\n        return bearishPatterns.reduce(function (state, pattern) {\n            return state || pattern.hasPattern(data);\n        }, false);\n    }\n}\nexport function bearish(data) {\n    return new BearishPatterns().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nimport Doji from './Doji';\nexport default class AbandonedBaby extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'AbandonedBaby';\n        this.requiredCount = 3;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let thirddaysOpen = data.open[2];\n        let thirddaysClose = data.close[2];\n        let thirddaysHigh = data.high[2];\n        let thirddaysLow = data.low[2];\n        let isFirstBearish = firstdaysClose < firstdaysOpen;\n        let dojiExists = new Doji().hasPattern({\n            \"open\": [seconddaysOpen],\n            \"close\": [seconddaysClose],\n            \"high\": [seconddaysHigh],\n            \"low\": [seconddaysLow]\n        });\n        let gapExists = ((seconddaysHigh < firstdaysLow) &&\n            (thirddaysLow > seconddaysHigh) &&\n            (thirddaysClose > thirddaysOpen));\n        let isThirdBullish = (thirddaysHigh < firstdaysOpen);\n        return (isFirstBearish && dojiExists && gapExists && isThirdBullish);\n    }\n}\nexport function abandonedbaby(data) {\n    return new AbandonedBaby().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class DarkCloudCover extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'DarkCloudCover';\n        this.requiredCount = 2;\n    }\n    logic(data) {\n        let firstdaysOpen = data.open[0];\n        let firstdaysClose = data.close[0];\n        let firstdaysHigh = data.high[0];\n        let firstdaysLow = data.low[0];\n        let seconddaysOpen = data.open[1];\n        let seconddaysClose = data.close[1];\n        let seconddaysHigh = data.high[1];\n        let seconddaysLow = data.low[1];\n        let firstdayMidpoint = ((firstdaysClose + firstdaysOpen) / 2);\n        let isFirstBullish = firstdaysClose > firstdaysOpen;\n        let isSecondBearish = seconddaysClose < seconddaysOpen;\n        let isDarkCloudPattern = ((seconddaysOpen > firstdaysHigh) &&\n            (seconddaysClose < firstdayMidpoint) &&\n            (seconddaysClose > firstdaysOpen));\n        return (isFirstBullish && isSecondBearish && isDarkCloudPattern);\n    }\n}\nexport function darkcloudcover(data) {\n    return new DarkCloudCover().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class DragonFlyDoji extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 1;\n        this.name = 'DragonFlyDoji';\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isOpenEqualsClose = this.approximateEqual(daysOpen, daysClose);\n        let isHighEqualsOpen = isOpenEqualsClose && this.approximateEqual(daysOpen, daysHigh);\n        let isLowEqualsClose = isOpenEqualsClose && this.approximateEqual(daysClose, daysLow);\n        return (isOpenEqualsClose && isHighEqualsOpen && !isLowEqualsClose);\n    }\n}\nexport function dragonflydoji(data) {\n    return new DragonFlyDoji().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class GraveStoneDoji extends CandlestickFinder {\n    constructor() {\n        super();\n        this.requiredCount = 1;\n        this.name = 'GraveStoneDoji';\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let isOpenEqualsClose = this.approximateEqual(daysOpen, daysClose);\n        let isHighEqualsOpen = isOpenEqualsClose && this.approximateEqual(daysOpen, daysHigh);\n        let isLowEqualsClose = isOpenEqualsClose && this.approximateEqual(daysClose, daysLow);\n        return (isOpenEqualsClose && isLowEqualsClose && !isHighEqualsOpen);\n    }\n}\nexport function gravestonedoji(data) {\n    return new GraveStoneDoji().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BullishSpinningTop extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BullishSpinningTop';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let bodyLength = Math.abs(daysClose - daysOpen);\n        let upperShadowLength = Math.abs(daysHigh - daysClose);\n        let lowerShadowLength = Math.abs(daysOpen - daysLow);\n        let isBullishSpinningTop = bodyLength < upperShadowLength &&\n            bodyLength < lowerShadowLength;\n        return isBullishSpinningTop;\n    }\n}\nexport function bullishspinningtop(data) {\n    return new BullishSpinningTop().hasPattern(data);\n}\n", "import CandlestickFinder from './CandlestickFinder';\nexport default class BearishSpinningTop extends CandlestickFinder {\n    constructor() {\n        super();\n        this.name = 'BearishSpinningTop';\n        this.requiredCount = 1;\n    }\n    logic(data) {\n        let daysOpen = data.open[0];\n        let daysClose = data.close[0];\n        let daysHigh = data.high[0];\n        let daysLow = data.low[0];\n        let bodyLength = Math.abs(daysClose - daysOpen);\n        let upperShadowLength = Math.abs(daysHigh - daysOpen);\n        let lowerShadowLength = Math.abs(daysHigh - daysLow);\n        let isBearishSpinningTop = bodyLength < upperShadowLength &&\n            bodyLength < lowerShadowLength;\n        return isBearishSpinningTop;\n    }\n}\nexport function bearishspinningtop(data) {\n    return new BearishSpinningTop().hasPattern(data);\n}\n", "/**\n * Cal<PERSON>ultes the fibonacci retracements for given start and end points\n *\n * If calculating for up trend start should be low and end should be high and vice versa\n *\n * returns an array of retracements level containing [0 , 23.6, 38.2, 50, 61.8, 78.6, 100, 127.2, 161.8, 261.8, 423.6]\n *\n * @export\n * @param {number} start\n * @param {number} end\n * @returns {number[]}\n */\nexport function fibonacciretracement(start, end) {\n    let levels = [0, 23.6, 38.2, 50, 61.8, 78.6, 100, 127.2, 161.8, 261.8, 423.6];\n    let retracements;\n    if (start < end) {\n        retracements = levels.map(function (level) {\n            let calculated = end - Math.abs(start - end) * (level) / 100;\n            return calculated > 0 ? calculated : 0;\n        });\n    }\n    else {\n        retracements = levels.map(function (level) {\n            let calculated = end + Math.abs(start - end) * (level) / 100;\n            return calculated > 0 ? calculated : 0;\n        });\n    }\n    return retracements;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class IchimokuCloudInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.conversionPeriod = 9;\n        this.basePeriod = 26;\n        this.spanPeriod = 52;\n        this.displacement = 26;\n    }\n}\nexport class IchimokuCloudOutput {\n}\nexport class IchimokuCloud extends Indicator {\n    constructor(input) {\n        super(input);\n        this.result = [];\n        var defaults = {\n            conversionPeriod: 9,\n            basePeriod: 26,\n            spanPeriod: 52,\n            displacement: 26\n        };\n        var params = Object.assign({}, defaults, input);\n        var currentConversionData = new LinkedList(params.conversionPeriod * 2, true, true, false);\n        var currentBaseData = new LinkedList(params.basePeriod * 2, true, true, false);\n        var currenSpanData = new LinkedList(params.spanPeriod * 2, true, true, false);\n        this.generator = (function* () {\n            let result;\n            let tick;\n            let period = Math.max(params.conversionPeriod, params.basePeriod, params.spanPeriod, params.displacement);\n            let periodCounter = 1;\n            tick = yield;\n            while (true) {\n                // Keep a list of lows/highs for the max period\n                currentConversionData.push(tick.high);\n                currentConversionData.push(tick.low);\n                currentBaseData.push(tick.high);\n                currentBaseData.push(tick.low);\n                currenSpanData.push(tick.high);\n                currenSpanData.push(tick.low);\n                if (periodCounter < period) {\n                    periodCounter++;\n                }\n                else {\n                    // Tenkan-sen (ConversionLine): (9-period high + 9-period low)/2))\n                    let conversionLine = (currentConversionData.periodHigh + currentConversionData.periodLow) / 2;\n                    // Kijun-sen (Base Line): (26-period high + 26-period low)/2))\n                    let baseLine = (currentBaseData.periodHigh + currentBaseData.periodLow) / 2;\n                    // Senkou Span A (Leading Span A): (Conversion Line + Base Line)/2))\n                    let spanA = (conversionLine + baseLine) / 2;\n                    // Senkou Span B (Leading Span B): (52-period high + 52-period low)/2))\n                    let spanB = (currenSpanData.periodHigh + currenSpanData.periodLow) / 2;\n                    // Senkou Span A / Senkou Span B offset by 26 periods\n                    // if(spanCounter < params.displacement) {\n                    // \tspanCounter++\n                    // } else {\n                    // \tspanA = spanAs.shift()\n                    // \tspanB = spanBs.shift()\n                    // }\n                    result = {\n                        conversion: conversionLine,\n                        base: baseLine,\n                        spanA: spanA,\n                        spanB: spanB\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        input.low.forEach((tick, index) => {\n            var result = this.generator.next({\n                high: input.high[index],\n                low: input.low[index],\n            });\n            if (result.value) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    nextValue(price) {\n        return this.generator.next(price).value;\n    }\n}\nIchimokuCloud.calculate = ichimokucloud;\nexport function ichimokucloud(input) {\n    Indicator.reverseInputs(input);\n    var result = new IchimokuCloud(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { SMA } from '../moving_averages/SMA';\nimport { EMA } from '../moving_averages/EMA';\nimport { ATR } from '../directionalmovement/ATR';\nexport class KeltnerChannelsInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.maPeriod = 20;\n        this.atrPeriod = 10;\n        this.useSMA = false;\n        this.multiplier = 1;\n    }\n}\nexport class KeltnerChannelsOutput extends IndicatorInput {\n}\n;\nexport class KeltnerChannels extends Indicator {\n    constructor(input) {\n        super(input);\n        var maType = input.useSMA ? SMA : EMA;\n        var maProducer = new maType({ period: input.maPeriod, values: [], format: (v) => { return v; } });\n        var atrProducer = new ATR({ period: input.atrPeriod, high: [], low: [], close: [], format: (v) => { return v; } });\n        var tick;\n        this.result = [];\n        this.generator = (function* () {\n            var KeltnerChannelsOutput;\n            var result;\n            tick = yield;\n            while (true) {\n                var { close } = tick;\n                var ma = maProducer.nextValue(close);\n                var atr = atrProducer.nextValue(tick);\n                if (ma != undefined && atr != undefined) {\n                    result = {\n                        middle: ma,\n                        upper: ma + (input.multiplier * (atr)),\n                        lower: ma - (input.multiplier * (atr))\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        var highs = input.high;\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: input.low[index],\n                close: input.close[index],\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nKeltnerChannels.calculate = keltnerchannels;\nexport function keltnerchannels(input) {\n    Indicator.reverseInputs(input);\n    var result = new KeltnerChannels(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nimport { ATR } from '../directionalmovement/ATR';\nimport LinkedList from '../Utils/FixedSizeLinkedList';\nexport class ChandelierExitInput extends IndicatorInput {\n    constructor() {\n        super(...arguments);\n        this.period = 22;\n        this.multiplier = 3;\n    }\n}\nexport class ChandelierExitOutput extends IndicatorInput {\n}\n;\nexport class ChandelierExit extends Indicator {\n    constructor(input) {\n        super(input);\n        var highs = input.high;\n        var lows = input.low;\n        var closes = input.close;\n        this.result = [];\n        var atrProducer = new ATR({ period: input.period, high: [], low: [], close: [], format: (v) => { return v; } });\n        var dataCollector = new LinkedList(input.period * 2, true, true, false);\n        this.generator = (function* () {\n            var result;\n            var tick = yield;\n            var atr;\n            while (true) {\n                var { high, low } = tick;\n                dataCollector.push(high);\n                dataCollector.push(low);\n                atr = atrProducer.nextValue(tick);\n                if ((dataCollector.totalPushed >= (2 * input.period)) && atr != undefined) {\n                    result = {\n                        exitLong: dataCollector.periodHigh - atr * input.multiplier,\n                        exitShort: dataCollector.periodLow + atr * input.multiplier\n                    };\n                }\n                tick = yield result;\n            }\n        })();\n        this.generator.next();\n        highs.forEach((tickHigh, index) => {\n            var tickInput = {\n                high: tickHigh,\n                low: lows[index],\n                close: closes[index],\n            };\n            var result = this.generator.next(tickInput);\n            if (result.value != undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    ;\n    nextValue(price) {\n        var result = this.generator.next(price);\n        if (result.value != undefined) {\n            return result.value;\n        }\n    }\n    ;\n}\nChandelierExit.calculate = chandelierexit;\nexport function chandelierexit(input) {\n    Indicator.reverseInputs(input);\n    var result = new ChandelierExit(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n;\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class CrossInput extends IndicatorInput {\n    constructor(lineA, lineB) {\n        super();\n        this.lineA = lineA;\n        this.lineB = lineB;\n    }\n}\nexport class CrossUp extends Indicator {\n    constructor(input) {\n        super(input);\n        this.lineA = input.lineA;\n        this.lineB = input.lineB;\n        var currentLineA = [];\n        var currentLineB = [];\n        const genFn = (function* () {\n            var current = yield;\n            var result = false;\n            while (true) {\n                currentLineA.unshift(current.valueA);\n                currentLineB.unshift(current.valueB);\n                result = current.valueA > current.valueB;\n                var pointer = 1;\n                while (result === true && currentLineA[pointer] >= currentLineB[pointer]) {\n                    if (currentLineA[pointer] > currentLineB[pointer]) {\n                        result = false;\n                    }\n                    else if (currentLineA[pointer] < currentLineB[pointer]) {\n                        result = true;\n                    }\n                    else if (currentLineA[pointer] === currentLineB[pointer]) {\n                        pointer += 1;\n                    }\n                }\n                if (result === true) {\n                    currentLineA = [current.valueA];\n                    currentLineB = [current.valueB];\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.result = [];\n        this.lineA.forEach((value, index) => {\n            var result = this.generator.next({\n                valueA: this.lineA[index],\n                valueB: this.lineB[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    static reverseInputs(input) {\n        if (input.reversedInput) {\n            input.lineA ? input.lineA.reverse() : undefined;\n            input.lineB ? input.lineB.reverse() : undefined;\n        }\n    }\n    nextValue(valueA, valueB) {\n        return this.generator.next({\n            valueA: valueA,\n            valueB: valueB\n        }).value;\n    }\n    ;\n}\nCrossUp.calculate = crossUp;\nexport function crossUp(input) {\n    Indicator.reverseInputs(input);\n    var result = new CrossUp(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "import { Indicator, IndicatorInput } from '../indicator/indicator';\nexport class CrossInput extends IndicatorInput {\n    constructor(lineA, lineB) {\n        super();\n        this.lineA = lineA;\n        this.lineB = lineB;\n    }\n}\nexport class CrossDown extends Indicator {\n    constructor(input) {\n        super(input);\n        this.lineA = input.lineA;\n        this.lineB = input.lineB;\n        var currentLineA = [];\n        var currentLineB = [];\n        const genFn = (function* () {\n            var current = yield;\n            var result = false;\n            while (true) {\n                currentLineA.unshift(current.valueA);\n                currentLineB.unshift(current.valueB);\n                result = current.valueA < current.valueB;\n                var pointer = 1;\n                while (result === true && currentLineA[pointer] <= currentLineB[pointer]) {\n                    if (currentLineA[pointer] < currentLineB[pointer]) {\n                        result = false;\n                    }\n                    else if (currentLineA[pointer] > currentLineB[pointer]) {\n                        result = true;\n                    }\n                    else if (currentLineA[pointer] === currentLineB[pointer]) {\n                        pointer += 1;\n                    }\n                }\n                if (result === true) {\n                    currentLineA = [current.valueA];\n                    currentLineB = [current.valueB];\n                }\n                current = yield result;\n            }\n        });\n        this.generator = genFn();\n        this.generator.next();\n        this.result = [];\n        this.lineA.forEach((value, index) => {\n            var result = this.generator.next({\n                valueA: this.lineA[index],\n                valueB: this.lineB[index]\n            });\n            if (result.value !== undefined) {\n                this.result.push(result.value);\n            }\n        });\n    }\n    static reverseInputs(input) {\n        if (input.reversedInput) {\n            input.lineA ? input.lineA.reverse() : undefined;\n            input.lineB ? input.lineB.reverse() : undefined;\n        }\n    }\n    nextValue(valueA, valueB) {\n        return this.generator.next({\n            valueA: valueA,\n            valueB: valueB\n        }).value;\n    }\n    ;\n}\nCrossDown.calculate = crossDown;\nexport function crossDown(input) {\n    Indicator.reverseInputs(input);\n    var result = new CrossDown(input).result;\n    if (input.reversedInput) {\n        result.reverse();\n    }\n    Indicator.reverseInputs(input);\n    return result;\n}\n", "export * from './lib/index.js';\nexport function getAvailableIndicators () {\n  let AvailableIndicators   = []\n  AvailableIndicators.push('sma');\n  AvailableIndicators.push('ema');\n  AvailableIndicators.push('wma');\n  AvailableIndicators.push('wema');\n  AvailableIndicators.push('macd');\n  AvailableIndicators.push('rsi');\n  AvailableIndicators.push('bollingerbands');\n  AvailableIndicators.push('adx');\n  AvailableIndicators.push('atr');\n  AvailableIndicators.push('truerange');\n  AvailableIndicators.push('roc');\n  AvailableIndicators.push('kst');\n  AvailableIndicators.push('psar');\n  AvailableIndicators.push('stochastic');\n  AvailableIndicators.push('williamsr');\n  AvailableIndicators.push('adl');\n  AvailableIndicators.push('obv');\n  AvailableIndicators.push('trix');\n\n  AvailableIndicators.push('cci');\n  AvailableIndicators.push('awesomeoscillator');\n  AvailableIndicators.push('forceindex');\n  AvailableIndicators.push('vwap');\n  AvailableIndicators.push('volumeprofile');\n  AvailableIndicators.push('renko');\n  AvailableIndicators.push('heikinashi');\n\n  AvailableIndicators.push('stochasticrsi');\n  AvailableIndicators.push('mfi');\n\n  AvailableIndicators.push('averagegain');\n  AvailableIndicators.push('averageloss');\n  AvailableIndicators.push('highest');\n  AvailableIndicators.push('lowest');\n  AvailableIndicators.push('sum');\n  AvailableIndicators.push('FixedSizeLinkedList');\n  AvailableIndicators.push('sd');\n  AvailableIndicators.push('bullish');\n  AvailableIndicators.push('bearish');\n  AvailableIndicators.push('abandonedbaby');\n  AvailableIndicators.push('doji');\n  AvailableIndicators.push('bearishengulfingpattern');\n  AvailableIndicators.push('bullishengulfingpattern');\n  AvailableIndicators.push('darkcloudcover');\n  AvailableIndicators.push('downsidetasukigap');\n  AvailableIndicators.push('dragonflydoji');\n  AvailableIndicators.push('gravestonedoji');\n  AvailableIndicators.push('bullishharami');\n  AvailableIndicators.push('bearishharami');\n  AvailableIndicators.push('bullishharamicross');\n  AvailableIndicators.push('bearishharamicross');\n  AvailableIndicators.push('eveningdojistar');\n  AvailableIndicators.push('eveningstar');\n  AvailableIndicators.push('morningdojistar');\n  AvailableIndicators.push('morningstar');\n  AvailableIndicators.push('bullishmarubozu');\n  AvailableIndicators.push('bearishmarubozu');\n  AvailableIndicators.push('piercingline');\n  AvailableIndicators.push('bullishspinningtop');\n  AvailableIndicators.push('bearishspinningtop');\n  AvailableIndicators.push('threeblackcrows');\n  AvailableIndicators.push('threewhitesoldiers');\n  AvailableIndicators.push('bullishhammerstick');\n  AvailableIndicators.push('bearishhammerstick');\n  AvailableIndicators.push('bullishinvertedhammerstick');\n  AvailableIndicators.push('bearishinvertedhammerstick');\n  AvailableIndicators.push('hammerpattern');\n  AvailableIndicators.push('hammerpatternunconfirmed');\n  AvailableIndicators.push('hangingman');\n  AvailableIndicators.push('hangingmanunconfirmed');\n  AvailableIndicators.push('shootingstar');\n  AvailableIndicators.push('shootingstarunconfirmed');\n  AvailableIndicators.push('tweezertop');\n  AvailableIndicators.push('tweezerbottom');\n\n  // AvailableIndicators.push('predictPattern');\n  // AvailableIndicators.push('hasDoubleBottom');\n  // AvailableIndicators.push('hasDoubleTop');\n  // AvailableIndicators.push('hasHeadAndShoulder');\n  // AvailableIndicators.push('hasInverseHeadAndShoulder');\n  // AvailableIndicators.push('isTrendingUp');\n  // AvailableIndicators.push('isTrendingDown');\n  AvailableIndicators.push('ichimokucloud');\n  \n  AvailableIndicators.push('keltnerchannels');\n  AvailableIndicators.push('chandelierexit');\n  AvailableIndicators.push('crossup');\n  AvailableIndicators.push('crossdown');\n  AvailableIndicators.push('crossover');\n  return AvailableIndicators;\n};\n\nlet AvailableIndicators = getAvailableIndicators();\nexport { AvailableIndicators }\n"], "names": ["nf", "sma", "LinkedList", "sd", "wema", "ema", "rsi", "stochastic", "FixedSizedLinkedList", "atr"], "mappings": ";;;;;AAAA,MAAM,IAAI,CAAC;IACP,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI;YACJ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,IAAI;YACJ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACpB;CACJ;AACD,AAAO,MAAM,UAAU,CAAC;IACpB,WAAW,GAAG;QACV,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;KACpB;IACD,IAAI,IAAI,GAAG;QACP,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;KACxC;IACD,IAAI,IAAI,GAAG;QACP,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;KACxC;IACD,IAAI,OAAO,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;KAC9C;IACD,IAAI,MAAM,GAAG;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;KACvB;IACD,IAAI,CAAC,IAAI,EAAE;QACP,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAC3B;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;KAClB;IACD,GAAG,GAAG;QACF,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,OAAO;SACV;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACjE,OAAO,IAAI,CAAC,IAAI,CAAC;SACpB;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;QAC5B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SAC1B;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;KACpB;IACD,KAAK,GAAG;QACJ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,OAAO;SACV;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACjE,OAAO,IAAI,CAAC,IAAI,CAAC;SACpB;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAC7B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC;KACpB;IACD,OAAO,CAAC,IAAI,EAAE;QACV,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAC3B;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;KAClB;IACD,cAAc,GAAG;QACb,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;YAC5C,OAAO,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;SAClC;;QAED,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE;YACxB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;SAC9B;aACI;YACD,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;SAChC;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;;QAEhC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QAC1B,OAAO,CAAC,IAAI,GAAG,SAAS,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;QACrB,OAAO,OAAO,CAAC,IAAI,CAAC;KACvB;IACD,aAAa,GAAG;QACZ,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC5B,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,OAAO;SACV;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACjE,OAAO,OAAO,CAAC,IAAI,CAAC;SACvB;QACD,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE;YACxB,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;SAC9B;aACI,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;SAC9B;aACI;YACD,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACjC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;SAChC;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAChC,OAAO,OAAO,CAAC,IAAI,CAAC;KACvB;IACD,WAAW,GAAG;QACV,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACxC,OAAO,IAAI,CAAC;KACf;IACD,IAAI,GAAG;QACH,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACtB,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO,IAAI,CAAC,IAAI,CAAC;SACpB;KACJ;CACJ;;AC/ID;;;AAGA,AACA,AAAe,MAAM,mBAAmB,SAAS,UAAU,CAAC;IACxD,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE;QACtD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACnC,OAAO,uCAAuC,EAAE;SACnD;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,UAAU,IAAI,EAAE;YACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACf,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB,CAAC;KACL;IACD,GAAG,CAAC,IAAI,EAAE;QACN,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;YAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;;YAEjB,IAAI,IAAI,CAAC,YAAY;gBACjB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU;oBACjC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,WAAW;gBAChB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS;oBAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClC,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;aACpD;SACJ;aACI;YACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACpB;;QAED,IAAI,IAAI,CAAC,YAAY;YACjB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI;gBACvB,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,EAAE;QACjC,IAAI,IAAI,CAAC,WAAW;YAChB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI;gBACtB,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE;QAChC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SAC1C;KACJ;IACD,CAAC,QAAQ,GAAG;QACR,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC,IAAI,EAAE,EAAE;YAChB,MAAM,IAAI,CAAC,OAAO,CAAC;SACtB;KACJ;IACD,mBAAmB,GAAG;QAClB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;QACnC,OAAO,IAAI,CAAC,IAAI,EAAE,EAAE;YAChB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;aAClC;YACD,AAAC;SACJ;QACD,AAAC;KACJ;IACD,kBAAkB,GAAG;QACjB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;QAClC,OAAO,IAAI,CAAC,IAAI,EAAE,EAAE;YAChB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;aACjC;YACD,AAAC;SACJ;QACD,AAAC;KACJ;CACJ;;AC1EM,MAAM,UAAU,CAAC;CACvB;AACD,AAAO,MAAM,UAAU,CAAC;IACpB,WAAW,GAAG;QACV,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;KACvB;CACJ;;ACpBD,IAAI,MAAM,GAAG,EAAE,CAAC;AAChB,AAAO,SAAS,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE;IAClC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;CACvB;AACD,AAAO,SAAS,SAAS,CAAC,GAAG,EAAE;IAC3B,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;CACtB;;ACLM,SAAS,MAAM,CAAC,CAAC,EAAE;IACtB,IAAI,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC;IACvC,IAAI,SAAS,EAAE;QACX,OAAO,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;KAC/C;IACD,OAAO,CAAC,CAAC;CACZ;;ACNM,MAAM,cAAc,CAAC;CAC3B;AACD,AAAO,AACN;AACD,AAAO,MAAM,SAAS,CAAC;IACnB,WAAW,CAAC,KAAK,EAAE;QACf,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,IAAIA,MAAE,CAAC;KACpC;IACD,OAAO,aAAa,CAAC,KAAK,EAAE;QACxB,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAClD,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAC9C,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAC9C,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAC5C,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAChD,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAClD,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;SAC3D;KACJ;IACD,SAAS,GAAG;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;KACtB;CACJ;;ACvBD;AACA,AACA,AACA;AACA,AAAO,AAMN;;AAED,AAAO,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,KAAK,IAAI,WAAW,MAAM,EAAE;YAC5B,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;YAC5B,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,MAAM,CAAC;YACX,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,IAAI,OAAO,GAAG,MAAM,EAAE;oBAClB,OAAO,EAAE,CAAC;oBACV,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACnB,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC;iBACvB;qBACI;oBACD,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC;oBACnC,MAAM,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC;oBAC1B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACtB;gBACD,OAAO,GAAG,MAAM,MAAM,CAAC;aAC1B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAClC;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB;AACD,AAAC;uBACsB;;AChEhB,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,QAAQ,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QAClC,IAAIC,MAAG,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjBA,MAAG,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9C,IAAI,KAAK,IAAI,aAAa;YACtB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,OAAO,CAAC;YACZ,OAAO,IAAI,EAAE;gBACT,IAAI,OAAO,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;oBAC7C,OAAO,GAAG,CAAC,CAAC,IAAI,GAAG,OAAO,IAAI,QAAQ,IAAI,OAAO,CAAC;oBAClD,IAAI,GAAG,MAAM,OAAO,CAAC;iBACxB;qBACI;oBACD,IAAI,GAAG,KAAK,CAAC;oBACb,OAAO,GAAGA,MAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC9B,IAAI,OAAO;wBACP,IAAI,GAAG,MAAM,OAAO,CAAC;iBAC5B;aACJ;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAClC;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB;;AClDM,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;YAC5B,IAAI,WAAW,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YAC5C,OAAO,IAAI,EAAE;gBACT,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE;oBACxB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBACpB;qBACI;oBACD,IAAI,CAAC,WAAW,EAAE,CAAC;oBACnB,IAAI,MAAM,GAAG,CAAC,CAAC;oBACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC9B,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC;qBACvD;oBACD,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC;oBACxB,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACnB;aACJ;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAChC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAClC;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAC;AACD,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB;;ACpDM,MAAM,IAAI,SAAS,SAAS,CAAC;IAChC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,QAAQ,GAAG,CAAC,GAAG,MAAM,CAAC;QAC1B,IAAIA,MAAG,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjBA,MAAG,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9C,IAAI,KAAK,IAAI,aAAa;YACtB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,OAAO,CAAC;YACZ,OAAO,IAAI,EAAE;gBACT,IAAI,OAAO,KAAK,SAAS,IAAI,IAAI,KAAK,SAAS,EAAE;oBAC7C,OAAO,GAAG,CAAC,CAAC,IAAI,GAAG,OAAO,IAAI,QAAQ,IAAI,OAAO,CAAC;oBAClD,IAAI,GAAG,MAAM,OAAO,CAAC;iBACxB;qBACI;oBACD,IAAI,GAAG,KAAK,CAAC;oBACb,OAAO,GAAGA,MAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAC9B,IAAI,OAAO,KAAK,SAAS;wBACrB,IAAI,GAAG,MAAM,OAAO,CAAC;iBAC5B;aACJ;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAClC;;CAEJ;AACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,AAAO,SAAS,IAAI,CAAC,KAAK,EAAE;IACxB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB;;ACrDD;;;AAGA,AACA,AACA,AACA,AAAO,AAON;AACD,AAAO,AACN;AACD,AAAO,MAAM,IAAI,SAAS,SAAS,CAAC;IAChC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,gBAAgB,GAAG,KAAK,CAAC,kBAAkB,GAAG,GAAG,GAAG,GAAG,CAAC;QAC5D,IAAI,YAAY,GAAG,KAAK,CAAC,cAAc,GAAG,GAAG,GAAG,GAAG,CAAC;QACpD,IAAI,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClH,IAAI,cAAc,GAAG,IAAI,gBAAgB,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClH,IAAI,gBAAgB,GAAG,IAAI,YAAY,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClH,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;YACxC,OAAO,IAAI,EAAE;gBACT,IAAI,KAAK,GAAG,KAAK,CAAC,UAAU,EAAE;oBAC1B,IAAI,GAAG,KAAK,CAAC;oBACb,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBACtC,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBACtC,KAAK,EAAE,CAAC;oBACR,SAAS;iBACZ;gBACD,IAAI,IAAI,IAAI,IAAI,EAAE;oBACd,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;oBACnB,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;iBAC7C;gBACD,SAAS,GAAG,IAAI,GAAG,MAAM,CAAC;gBAC1B,IAAI,GAAG,OAAO;;;oBAGV,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;oBAClB,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,SAAS;oBAC3C,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;iBAC9D,CAAC,CAAC;gBACH,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;aACzC;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YAC3B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,OAAO,MAAM,CAAC;KACjB;;CAEJ;AACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,AAAO,SAAS,IAAI,CAAC,KAAK,EAAE;IACxB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC3EM,MAAM,WAAW,SAAS,SAAS,CAAC;IACvC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,CAAC,WAAW,MAAM,EAAE;YACjC,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,CAAC;YACZ,IAAI,IAAI,CAAC;YACT,IAAI,SAAS,GAAG,YAAY,CAAC;YAC7B,YAAY,GAAG,KAAK,CAAC;YACrB,OAAO,IAAI,EAAE;gBACT,IAAI,GAAG,YAAY,GAAG,SAAS,CAAC;gBAChC,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;gBAC3B,IAAI,IAAI,GAAG,CAAC,EAAE;oBACV,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC;iBAC5B;gBACD,IAAI,OAAO,GAAG,MAAM,EAAE;oBAClB,OAAO,EAAE,CAAC;iBACb;qBACI,IAAI,OAAO,KAAK,SAAS,EAAE;oBAC5B,OAAO,GAAG,OAAO,GAAG,MAAM,CAAC;iBAC9B;qBACI;oBACD,OAAO,GAAG,CAAC,CAAC,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC;iBACxD;gBACD,SAAS,GAAG,YAAY,CAAC;gBACzB,OAAO,GAAG,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;gBAChE,YAAY,GAAG,MAAM,OAAO,CAAC;aAChC;SACJ,EAAE,MAAM,CAAC,CAAC;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACrB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC;AACpC,AAAO,SAAS,WAAW,CAAC,KAAK,EAAE;IAC/B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC3C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC1DM,MAAM,WAAW,SAAS,SAAS,CAAC;IACvC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,CAAC,WAAW,MAAM,EAAE;YACjC,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,CAAC;YACZ,IAAI,IAAI,CAAC;YACT,IAAI,SAAS,GAAG,YAAY,CAAC;YAC7B,YAAY,GAAG,KAAK,CAAC;YACrB,OAAO,IAAI,EAAE;gBACT,IAAI,GAAG,SAAS,GAAG,YAAY,CAAC;gBAChC,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;gBAC3B,IAAI,IAAI,GAAG,CAAC,EAAE;oBACV,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC;iBAC5B;gBACD,IAAI,OAAO,GAAG,MAAM,EAAE;oBAClB,OAAO,EAAE,CAAC;iBACb;qBACI,IAAI,OAAO,KAAK,SAAS,EAAE;oBAC5B,OAAO,GAAG,OAAO,GAAG,MAAM,CAAC;iBAC9B;qBACI;oBACD,OAAO,GAAG,CAAC,CAAC,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC;iBACxD;gBACD,SAAS,GAAG,YAAY,CAAC;gBACzB,OAAO,GAAG,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;gBAChE,YAAY,GAAG,MAAM,OAAO,CAAC;aAChC;SACJ,EAAE,MAAM,CAAC,CAAC;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACrB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC;AACpC,AAAO,SAAS,WAAW,CAAC,KAAK,EAAE;IAC/B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC3C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC7DD;;;AAGA,AACA,AACA,AACA,AAAO,AACN;AACD,AAAO,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,YAAY,GAAG,IAAI,WAAW,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QACnE,IAAI,YAAY,GAAG,IAAI,WAAW,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QACnE,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,CAAC,SAAS,GAAG,CAAC,WAAW,MAAM,EAAE;YACjC,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,WAAW,EAAE,WAAW,EAAE,EAAE,EAAE,UAAU,CAAC;YAC7C,OAAO,IAAI,EAAE;gBACT,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC9C,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC9C,IAAI,CAAC,WAAW,KAAK,SAAS,MAAM,WAAW,KAAK,SAAS,CAAC,EAAE;oBAC5D,IAAI,WAAW,KAAK,CAAC,EAAE;wBACnB,UAAU,GAAG,GAAG,CAAC;qBACpB;yBACI,IAAI,WAAW,KAAK,CAAC,EAAE;wBACxB,UAAU,GAAG,CAAC,CAAC;qBAClB;yBACI;wBACD,EAAE,GAAG,WAAW,GAAG,WAAW,CAAC;wBAC/B,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;wBACxB,UAAU,GAAG,UAAU,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;qBAChE;iBACJ;gBACD,KAAK,EAAE,CAAC;gBACR,OAAO,GAAG,MAAM,UAAU,CAAC;aAC9B;SACJ,EAAE,MAAM,CAAC,CAAC;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACrB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACtDM,MAAM,EAAE,SAAS,SAAS,CAAC;IAC9B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAIA,MAAG,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC;YACT,IAAI,UAAU,GAAG,IAAIC,mBAAU,CAAC,MAAM,CAAC,CAAC;YACxC,AAAC;YACD,IAAI,GAAG,KAAK,CAAC;YACb,IAAI,EAAE,CAAC;YACP,OAAO,IAAI,EAAE;gBACT,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,GAAGD,MAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC3B,IAAI,IAAI,EAAE;oBACN,IAAI,GAAG,GAAG,CAAC,CAAC;oBACZ,KAAK,IAAI,CAAC,IAAI,UAAU,CAAC,QAAQ,EAAE,EAAE;wBACjC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;qBACzC;oBACD,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC;iBAClC;gBACD,IAAI,GAAG,MAAM,EAAE,CAAC;aACnB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,IAAI,SAAS;YAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC5C;;CAEJ;AACD,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC;AAClB,AAAO,SAAS,EAAE,CAAC,KAAK,EAAE;IACtB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAClC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACpDM,MAAM,cAAc,SAAS,SAAS,CAAC;IAC1C,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAIA,MAAG,EAAEE,KAAE,CAAC;QACZ,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjBF,MAAG,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5EE,KAAE,GAAG,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1E,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,OAAO,CAAC;YACZ,IAAI,MAAM,CAAC;YACX,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,OAAO,GAAGF,MAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC9B,MAAM,GAAGE,KAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC5B,IAAI,OAAO,EAAE;oBACT,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC7B,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;oBAChD,IAAI,KAAK,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC;oBAChD,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,IAAI,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;oBAClD,MAAM,GAAG;wBACL,MAAM,EAAE,MAAM;wBACd,KAAK,EAAE,KAAK;wBACZ,KAAK,EAAE,KAAK;wBACZ,EAAE,EAAE,EAAE;qBACT,CAAC;iBACL;gBACD,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC;AAC1C,AAAO,SAAS,cAAc,CAAC,KAAK,EAAE;IAClC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC9C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AClED;AACA,AAAO,MAAM,eAAe,SAAS,SAAS,CAAC;IAC3C,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,KAAK,IAAI,WAAW,MAAM,EAAE;YAC5B,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,CAAC;YAC5B,IAAI,GAAG,GAAG,CAAC,CAAC;YACZ,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,OAAO,IAAI,EAAE;gBACT,IAAI,OAAO,GAAG,MAAM,EAAE;oBAClB,OAAO,EAAE,CAAC;oBACV,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC;oBACpB,MAAM,GAAG,SAAS,CAAC;iBACtB;qBACI,IAAI,OAAO,IAAI,MAAM,EAAE;oBACxB,OAAO,EAAE,CAAC;oBACV,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC;oBACpB,MAAM,GAAG,GAAG,CAAC;iBAChB;qBACI;oBACD,MAAM,GAAG,MAAM,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC;iBACjD;gBACD,OAAO,GAAG,MAAM,MAAM,CAAC;aAC1B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;KAClC;;CAEJ;AACD,eAAe,CAAC,SAAS,GAAG,eAAe,CAAC;AAC5C,AAAO,SAAS,eAAe,CAAC,KAAK,EAAE;IACnC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC/C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB;AACD,AAAC;uBACsB;;ACnDhB,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;YAC7B,OAAO,oCAAoC,EAAE;SAChD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,OAAO,CAAC;YACZ,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,IAAI,CAAC;YACT,OAAO,IAAI,EAAE;gBACT,IAAI,IAAI,EAAE;oBACN,IAAI,MAAM,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxC,IAAI,QAAQ,IAAI,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;oBACxC,OAAO,GAAG,MAAM,CAAC,CAAC,QAAQ,GAAG,MAAM,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC;iBACxE;gBACD,IAAI,GAAG,OAAO,CAAC;gBACf,OAAO,GAAG,MAAM,OAAO,CAAC;aAC3B;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;aACnB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACtC,CAAC,CAAC;KACN;;IAED,OAAO,SAAS,CAAC,KAAK,EAAE;QACpB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACnC,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,MAAM,CAAC,OAAO,EAAE,CAAC;SACpB;QACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/B,OAAO,MAAM,CAAC;KACjB;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;;ACxDD;;;AAGA,AAAO,AACN;AACD,AAAC;AACD,AAAO,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;YAC7B,OAAO,oCAAoC,EAAE;SAChD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,IAAI,CAAC;YACT,OAAO,IAAI,EAAE;gBACT,IAAI,IAAI,EAAE;oBACN,IAAI,MAAM,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxC,IAAI,QAAQ,IAAI,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;oBACxC,MAAM,GAAG,MAAM,CAAC,CAAC,MAAM,GAAG,QAAQ,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;iBACnE;gBACD,IAAI,GAAG,OAAO,CAAC;gBACf,OAAO,GAAG,MAAM,MAAM,CAAC;aAC1B;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;aACnB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACtC,CAAC,CAAC;KACN;;IAED,OAAO,SAAS,CAAC,KAAK,EAAE;QACpB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;QACnC,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,MAAM,CAAC,OAAO,EAAE,CAAC;SACpB;QACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/B,OAAO,MAAM,CAAC;KACjB;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;;AC7CM,MAAM,SAAS,SAAS,SAAS,CAAC;IACrC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;YAC7B,OAAO,oCAAoC,EAAE;SAChD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,aAAa,EAAE,MAAM,CAAC;YAC1B,OAAO,IAAI,EAAE;gBACT,IAAI,aAAa,KAAK,SAAS,EAAE;oBAC7B,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;oBAC9B,OAAO,GAAG,MAAM,MAAM,CAAC;iBAC1B;gBACD,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC;gBACpO,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC9B,IAAI,MAAM,IAAI,SAAS,EAAE;oBACrB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;iBAC3B;gBACD,OAAO,GAAG,MAAM,MAAM,CAAC;aAC1B;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACvB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC;AAChC,AAAO,SAAS,SAAS,CAAC,KAAK,EAAE;IAC7B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACzC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACzDM,MAAM,SAAS,SAAS,cAAc,CAAC;CAC7C;AACD,AAAC;AACD,AAAO,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC;YACjB,IAAI,EAAE,EAAE;YACR,GAAG,EAAE,EAAE;SACV,CAAC,CAAC;QACH,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC;YAClB,IAAI,EAAE,EAAE;YACR,GAAG,EAAE,EAAE;SACV,CAAC,CAAC;QACH,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/F,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/F,IAAI,KAAK,GAAG,IAAI,eAAe,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9F,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnF,IAAI,EAAE,GAAG,IAAI,SAAS,CAAC;YACnB,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;SACZ,CAAC,CAAC;QACH,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;YACvE,OAAO,2CAA2C,EAAE;SACvD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,SAAS,CAAC;QACV,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC;YACtE,OAAO,GAAG,CAAC,CAAC;YACZ,QAAQ,GAAG,CAAC,CAAC;YACb,QAAQ,GAAG,CAAC,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,IAAI,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,MAAM,KAAK,SAAS,EAAE;oBACtB,IAAI,GAAG,KAAK,CAAC;oBACb,SAAS;iBACZ;gBACD,IAAI,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBACtC,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACzC,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACzC,IAAI,CAAC,OAAO,IAAI,SAAS,MAAM,QAAQ,IAAI,SAAS,CAAC,KAAK,QAAQ,IAAI,SAAS,CAAC,EAAE;oBAC9E,OAAO,GAAG,CAAC,QAAQ,IAAI,GAAG,GAAG,OAAO,CAAC;oBACrC,OAAO,GAAG,CAAC,QAAQ,IAAI,GAAG,GAAG,OAAO,CAAC;oBACrC,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC;oBACzC,IAAI,KAAK,IAAI,OAAO,GAAG,OAAO,CAAC,CAAC;oBAChC,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC;oBAChC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;;iBAExC;gBACD,IAAI,GAAG,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;aAChE;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACvB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,SAAS,EAAE;gBAC5D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;aACrH;SACJ,CAAC,CAAC;KACN;;;IAGD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE;YAChD,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;SACvG;KACJ;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC9FM,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;YACvE,OAAO,2CAA2C,EAAE;SACvD;QACD,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC;YAC1B,GAAG,EAAE,EAAE;YACP,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;SACZ,CAAC,CAAC;QACH,IAAIC,OAAI,GAAG,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,YAAY,EAAE,MAAM,CAAC;YACzB,AAAC;YACD,OAAO,IAAI,EAAE;gBACT,MAAM,GAAG,SAAS,CAAC,SAAS,CAAC;oBACzB,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,KAAK,EAAE,IAAI,CAAC,KAAK;iBACpB,CAAC,CAAC;gBACH,IAAI,MAAM,KAAK,SAAS,EAAE;oBACtB,YAAY,GAAG,SAAS,CAAC;iBAC5B;qBACI;oBACD,YAAY,GAAGA,OAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;iBACzC;gBACD,IAAI,GAAG,MAAM,YAAY,CAAC;aAC7B;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACvB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC1C;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACvEM,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,WAAW,GAAG,IAAIF,mBAAU,CAAC,MAAM,CAAC,CAAC;YACzC,AAAC;YACD,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,GAAG,CAAC;YACR,OAAO,IAAI,EAAE;gBACT,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACvB,IAAI,KAAK,GAAG,MAAM,EAAE;oBAChB,KAAK,EAAE,CAAC;iBACX;qBACI;oBACD,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,WAAW,CAAC,SAAS,KAAK,WAAW,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC;iBAC1E;gBACD,IAAI,GAAG,MAAM,GAAG,CAAC;aACpB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;YAC7D,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACxC;KACJ;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAC;AACD,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC/CM,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;QAC/B,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;QAC/B,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;QAC/B,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;QAC/B,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACtC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QACpD,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QACpD,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QACpD,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QACpD,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClF,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClF,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClF,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClF,IAAI,SAAS,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5F,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,CAAC;QACvG,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,GAAG,CAAC;YACR,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;YAC/C,OAAO,IAAI,EAAE;gBACT,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,KAAK,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;gBAC5E,KAAK,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;gBAC5E,KAAK,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;gBAC5E,KAAK,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;gBAC5E,IAAI,KAAK,GAAG,WAAW,EAAE;oBACrB,KAAK,EAAE,CAAC;iBACX;qBACI;oBACD,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;iBAC/D;gBACD,MAAM,GAAG,CAAC,GAAG,KAAK,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;gBACpE,MAAM,GAAG,GAAG,KAAK,SAAS,GAAG;oBACzB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC;oBAChB,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,SAAS;iBAC9C,GAAG,SAAS,CAAC;gBACd,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,IAAI,SAAS;YAC7B,OAAO,UAAU,CAAC,KAAK,CAAC;KAC/B;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACpFD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,AAAO,AACN;AACD,AAAC;AACD,AAAO,MAAM,IAAI,SAAS,SAAS,CAAC;IAChC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC;QAC3B,IAAI,KAAK,GAAG,WAAW,IAAI,EAAE,GAAG,EAAE;YAC9B,IAAI,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC;YACjC,IAAI,EAAE,GAAG,IAAI,CAAC;YACd,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE;gBACT,IAAI,IAAI,EAAE;oBACN,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,OAAO,GAAG,GAAG,CAAC,CAAC;oBACpC,IAAI,EAAE,EAAE;wBACJ,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC5C,IAAI,IAAI,CAAC,IAAI,GAAG,OAAO,EAAE;4BACrB,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;4BACpB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;yBACvC;wBACD,AAAC;qBACJ;yBACI;wBACD,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC9C,IAAI,IAAI,CAAC,GAAG,GAAG,OAAO,EAAE;4BACpB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;4BACnB,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC;yBACvC;qBACJ;oBACD,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE;wBACpD,KAAK,GAAG,IAAI,CAAC;wBACb,GAAG,GAAG,OAAO,CAAC;wBACd,EAAE,GAAG,CAAC,EAAE,CAAC;wBACT,OAAO,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;qBACxC;iBACJ;qBACI;;oBAED,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;oBACf,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;iBACvB;gBACD,QAAQ,GAAG,IAAI,CAAC;gBAChB,IAAI,IAAI;oBACJ,IAAI,GAAG,IAAI,CAAC;gBAChB,IAAI,GAAG,MAAM,GAAG,CAAC;aACpB;SACJ,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;aACnB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,KAAK,SAAS;YAC9B,OAAO,UAAU,CAAC,KAAK,CAAC;KAC/B;;CAEJ;AACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,AAAO,SAAS,IAAI,CAAC,KAAK,EAAE;IACxB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACpGM,MAAM,UAAU,SAAS,SAAS,CAAC;IACtC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACtC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;YACvE,OAAO,2CAA2C,EAAE;SACvD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;;;;;;;QAOjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,eAAe,GAAG,IAAIA,mBAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,cAAc,GAAG,IAAIA,mBAAU,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACzD,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC;gBACf,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;aAC/B,CAAC,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,CAAC;YACT,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE;gBACT,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI,KAAK,GAAG,MAAM,EAAE;oBAChB,KAAK,EAAE,CAAC;oBACR,IAAI,GAAG,KAAK,CAAC;oBACb,SAAS;iBACZ;gBACD,IAAI,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;gBACzC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,KAAK,eAAe,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,GAAG,CAAC;gBAC9E,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,GAAG,MAAM;oBACT,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBACZ,CAAC,EAAE,CAAC,CAAC,KAAK,SAAS,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS;iBAC/C,CAAC;aACL;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACvB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,KAAK,SAAS;YAC9B,OAAO,UAAU,CAAC,KAAK,CAAC;KAC/B;;CAEJ;AACD,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC;AAClC,AAAO,SAAS,UAAU,CAAC,KAAK,EAAE;IAC9B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC1C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACtFM,MAAM,SAAS,SAAS,SAAS,CAAC;IACrC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;YACvE,OAAO,2CAA2C,EAAE;SACvD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;;;;;QAKjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAI,eAAe,GAAG,IAAIA,mBAAU,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC1D,IAAI,cAAc,GAAG,IAAIA,mBAAU,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACzD,IAAI,SAAS,CAAC;YACd,IAAI,UAAU,CAAC;YACf,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,SAAS,CAAC;YACd,OAAO,IAAI,EAAE;gBACT,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI,KAAK,GAAG,MAAM,EAAE;oBAChB,KAAK,EAAE,CAAC;oBACR,IAAI,GAAG,KAAK,CAAC;oBACb,SAAS;iBACZ;gBACD,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC;gBACrC,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;gBACxC,SAAS,GAAG,MAAM,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,KAAK,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAChF,IAAI,GAAG,MAAM,SAAS,CAAC;aAC1B;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACvB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,IAAI,SAAS;YAC7B,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KAC5C;;CAEJ;AACD,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC;AAChC,AAAO,SAAS,SAAS,CAAC,KAAK,EAAE;IAC7B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACzC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACzED;;;AAGA,AACA,AAAO,AACN;AACD,AAAO,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YAC5G,OAAO,oDAAoD,EAAE;SAChE;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,IAAI,CAAC;YACT,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,IAAI,mBAAmB,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxG,mBAAmB,GAAG,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC;gBAC3E,IAAI,eAAe,GAAG,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC;gBACxD,MAAM,GAAG,MAAM,GAAG,eAAe,CAAC;gBAClC,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;aACnC;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,KAAK;YAC/B,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;gBACpB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;aACzB,CAAC;YACF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACpDM,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,GAAG,CAAC,CAAC;YACf,IAAI,IAAI,CAAC;YACT,IAAI,SAAS,CAAC;YACd,IAAI,GAAG,KAAK,CAAC;YACb,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE;gBAChD,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;gBACvB,IAAI,GAAG,KAAK,CAAC;aAChB;YACD,OAAO,IAAI,EAAE;gBACT,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,EAAE;oBACxB,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;iBACjC;qBACI,IAAI,IAAI,CAAC,KAAK,GAAG,SAAS,EAAE;oBAC7B,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;iBACjC;gBACD,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;gBACvB,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;YAC7B,IAAI,SAAS,GAAG;gBACZ,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;gBACpB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;aACzB,CAAC;YACF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC5DD;;;AAGA,AACA,AACA,AACA,AACA,AAAO,AACN;AACD,AAAC;AACD,AAAO,MAAM,IAAI,SAAS,SAAS,CAAC;IAChC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC;QAC9B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAIG,MAAG,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChF,IAAI,QAAQ,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrF,IAAI,aAAa,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC1F,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC/E,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE;gBACT,IAAI,UAAU,GAAGA,MAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,cAAc,GAAG,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,SAAS,CAAC;gBAC7E,IAAI,oBAAoB,GAAG,cAAc,GAAG,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC;gBAChG,IAAI,MAAM,GAAG,oBAAoB,GAAG,OAAO,CAAC,SAAS,CAAC,oBAAoB,CAAC,GAAG,SAAS,CAAC;gBACxF,IAAI,GAAG,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC;aACpD;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,KAAK,SAAS;YAC9B,OAAO,UAAU,CAAC,KAAK,CAAC;KAC/B;;CAEJ;AACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,AAAO,SAAS,IAAI,CAAC,KAAK,EAAE;IACxB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC/CM,MAAM,UAAU,SAAS,SAAS,CAAC;IACtC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC;QAC/B,IAAI,GAAG,OAAO,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,EAAE;YACvC,OAAO,yCAAyC,EAAE;SACrD;QACD,IAAI,aAAa,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,YAAY,GAAG,KAAK,CAAC;YACzB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,UAAU,CAAC;YACf,OAAO,IAAI,EAAE;gBACT,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC;gBAC7D,YAAY,GAAG,IAAI,CAAC;gBACpB,IAAI,GAAG,MAAM,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACpD;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;gBACpB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;aACzB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;;IAGD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS,EAAE;YACrB,OAAO,MAAM,CAAC;SACjB;KACJ;;CAEJ;AACD,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC;AAClC,AAAO,SAAS,UAAU,CAAC,KAAK,EAAE;IAC9B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC1C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACvDM,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,YAAY,GAAG,IAAIH,mBAAU,CAAC,MAAM,CAAC,CAAC;QAC1C,AAAC;QACD,IAAI,eAAe,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5F,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;YACvE,OAAO,2CAA2C,EAAE;SACvD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE;gBACT,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;gBACjD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACtB,IAAI,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;gBAC1C,IAAI,aAAa,GAAG,IAAI,CAAC;gBACzB,IAAI,GAAG,CAAC;gBACR,IAAI,GAAG,GAAG,CAAC,CAAC;gBACZ,IAAI,KAAK,IAAI,SAAS,EAAE;;;;oBAIpB,KAAK,IAAI,CAAC,IAAI,YAAY,CAAC,QAAQ,EAAE,EAAE;wBACnC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;qBACrC;;oBAED,aAAa,GAAG,GAAG,GAAG,MAAM,CAAC;oBAC7B,GAAG,GAAG,CAAC,EAAE,GAAG,KAAK,KAAK,QAAQ,GAAG,aAAa,CAAC,CAAC;iBACnD;gBACD,IAAI,GAAG,MAAM,GAAG,CAAC;aACpB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACvB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;;IAGD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS,EAAE;YACrB,OAAO,MAAM,CAAC;SACjB;KACJ;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACzEM,MAAM,iBAAiB,SAAS,SAAS,CAAC;IAC7C,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAClC,IAAI,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;QAClC,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QAC1D,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,WAAW,CAAC;YAChB,IAAI,YAAY,CAAC;YACjB,IAAI,YAAY,CAAC;YACjB,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;gBACzC,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC9C,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;gBAC9C,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,SAAS,EAAE;oBAC1D,MAAM,GAAG,YAAY,GAAG,YAAY,CAAC;iBACxC;gBACD,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,KAAK;YAC/B,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;aACnB,CAAC;YACF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;aAC/C;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACpC;KACJ;;CAEJ;AACD,iBAAiB,CAAC,SAAS,GAAG,iBAAiB,CAAC;AAChD,AAAO,SAAS,iBAAiB,CAAC,KAAK,EAAE;IACrC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACjD,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC1DM,MAAM,IAAI,SAAS,SAAS,CAAC;IAChC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE;YACvE,OAAO,2CAA2C,EAAE;SACvD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI,eAAe,GAAG,CAAC,CAAC;YACxB,IAAI,gBAAgB,GAAG,CAAC,CAAC;YACzB,OAAO,IAAI,EAAE;gBACT,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;gBAC3D,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC;gBACvC,eAAe,GAAG,eAAe,GAAG,KAAK,CAAC;gBAC1C,gBAAgB,GAAG,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC;gBAClD,IAAI,GAAG,MAAM,eAAe,GAAG,gBAAgB,CAAC;gBAChD,AAAC;aACJ;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;gBAClB,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;gBACpB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;aACzB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;;IAGD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,IAAI,MAAM,IAAI,SAAS,EAAE;YACrB,OAAO,MAAM,CAAC;SACjB;KACJ;;CAEJ;AACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,AAAO,SAAS,IAAI,CAAC,KAAK,EAAE;IACxB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACpC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACzDM,SAAS,yBAAyB,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE;IAC9D,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC;CACzE;AACD,AAAO,MAAM,aAAa,SAAS,SAAS,CAAC;IACzC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC;QAC1B,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YAC5G,OAAO,oDAAoD,EAAE;SAChE;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;QAC3D,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;QAC3D,IAAI,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC;QAClC,IAAI,OAAO,GAAG,GAAG,CAAC;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC3B,IAAI,UAAU,GAAG,OAAO,CAAC;YACzB,IAAI,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAC;YACrC,OAAO,GAAG,QAAQ,CAAC;YACnB,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,WAAW,GAAG,CAAC,CAAC;YACpB,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;gBACxD,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAClC,IAAI,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACrC,IAAI,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACvC,IAAI,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,CAAC,EAAE;oBAC7E,WAAW,GAAG,WAAW,GAAG,cAAc,CAAC;oBAC3C,IAAI,YAAY,GAAG,aAAa,EAAE;wBAC9B,aAAa,GAAG,aAAa,GAAG,cAAc,CAAC;qBAClD;yBACI;wBACD,aAAa,GAAG,aAAa,GAAG,cAAc,CAAC;qBAClD;iBACJ;aACJ;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACb,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW;aAClE,CAAC,CAAC;SACN;KACJ;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,6CAA6C,EAAE;KACzD;;CAEJ;AACD,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC;AACxC,AAAO,SAAS,aAAa,CAAC,KAAK,EAAE;IACjC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC7C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACrED;;;AAGA,AACA,AAAO,AACN;AACD,AAAO,MAAM,YAAY,SAAS,SAAS,CAAC;IACxC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,OAAO,IAAI,EAAE;gBACT,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK,IAAI,CAAC,CAAC;aAChF;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC/B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvB,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;gBACrB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;aAC5B,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SAClC,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,OAAO,MAAM,CAAC;KACjB;;CAEJ;AACD,YAAY,CAAC,SAAS,GAAG,YAAY,CAAC;AACtC,AAAO,SAAS,YAAY,CAAC,KAAK,EAAE;IAChC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC5C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC1CD;;;AAGA,AACA,AACA,AACA,AAAO,AACN;AACD,AAAO,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,YAAY,GAAG,IAAI,YAAY,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QACtE,IAAI,YAAY,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,YAAY,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE;YAC5G,OAAO,oDAAoD,EAAE;SAChE;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,SAAS,CAAC;YACd,IAAI,qBAAqB,CAAC;YAC1B,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,cAAc,CAAC;YACnB,IAAI,qBAAqB,CAAC;YAC1B,IAAI,iBAAiB,GAAG,IAAI,CAAC;YAC7B,IAAI,mBAAmB,GAAG,IAAI,CAAC;YAC/B,IAAI,GAAG,KAAK,CAAC;YACb,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;YACvB,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBACxC,IAAI,aAAa,GAAG,CAAC,CAAC;gBACtB,IAAI,aAAa,GAAG,CAAC,CAAC;gBACtB,iBAAiB,GAAG,YAAY,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;gBACjE,YAAY,GAAG,iBAAiB,GAAG,MAAM,CAAC;gBAC1C,IAAI,CAAC,iBAAiB,IAAI,IAAI,MAAM,mBAAmB,IAAI,IAAI,CAAC,EAAE;oBAC9D,iBAAiB,GAAG,mBAAmB,GAAG,aAAa,GAAG,YAAY,GAAG,aAAa,GAAG,YAAY,CAAC;oBACtG,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBACjC,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBACjC,qBAAqB,GAAG,YAAY,CAAC,SAAS,CAAC;oBAC/C,qBAAqB,GAAG,YAAY,CAAC,SAAS,CAAC;oBAC/C,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,MAAM,MAAM,YAAY,CAAC,WAAW,IAAI,MAAM,CAAC,EAAE;wBAC9E,cAAc,GAAG,qBAAqB,GAAG,qBAAqB,CAAC;wBAC/D,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;qBAC7C;iBACJ;gBACD,mBAAmB,GAAG,iBAAiB,CAAC;gBACxC,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,KAAK;YAC/B,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;gBACpB,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC;aACzB,CAAC;YACF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzD;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YAC3B,QAAQ,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;SAChD;KACJ;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC5EM,MAAM,aAAa,SAAS,SAAS,CAAC;IACzC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;QAChC,IAAI,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAC;QAC9C,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,IAAII,MAAG,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;YACrD,IAAIC,aAAU,GAAG,IAAI,UAAU,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;YACnH,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC;gBACf,MAAM,EAAE,OAAO;gBACf,MAAM,EAAE,EAAE;gBACV,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;aAC/B,CAAC,CAAC;YACH,IAAI,OAAO,EAAE,aAAa,EAAE,CAAC,EAAE,MAAM,CAAC;YACtC,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,OAAO,IAAI,EAAE;gBACT,OAAO,GAAGD,MAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC9B,IAAI,OAAO,KAAK,SAAS,EAAE;oBACvB,IAAI,eAAe,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;oBACtE,aAAa,GAAGC,aAAU,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;oBACtD,IAAI,aAAa,KAAK,SAAS,IAAI,aAAa,CAAC,CAAC,KAAK,SAAS,EAAE;wBAC9D,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;wBACpC,IAAI,CAAC,KAAK,SAAS;4BACf,MAAM,GAAG;gCACL,QAAQ,EAAE,aAAa,CAAC,CAAC;gCACzB,CAAC,EAAE,aAAa,CAAC,CAAC;gCAClB,CAAC,EAAE,CAAC;6BACP,CAAC;qBACT;iBACJ;gBACD,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC5B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,UAAU,CAAC,KAAK,KAAK,SAAS;YAC9B,OAAO,UAAU,CAAC,KAAK,CAAC;KAC/B;;CAEJ;AACD,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC;AACxC,AAAO,SAAS,aAAa,CAAC,KAAK,EAAE;IACjC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC7C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC3EM,MAAM,OAAO,SAAS,SAAS,CAAC;IACnC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,UAAU,GAAG,IAAIC,mBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC;YACT,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,UAAU,CAAC,WAAW,IAAI,MAAM,EAAE;oBAClC,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC;iBAChC;gBACD,IAAI,GAAG,MAAM,IAAI,CAAC;aACrB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;YAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YAC3B,OAAO,MAAM,CAAC,KAAK,CAAC;SACvB;KACJ;;CAEJ;AACD,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC;AAC5B,AAAO,SAAS,OAAO,CAAC,KAAK,EAAE;IAC3B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACvC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC/CM,MAAM,MAAM,SAAS,SAAS,CAAC;IAClC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,UAAU,GAAG,IAAIA,mBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC;YACT,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,UAAU,CAAC,WAAW,IAAI,MAAM,EAAE;oBAClC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC;iBAC/B;gBACD,IAAI,GAAG,MAAM,IAAI,CAAC;aACrB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;YAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YAC3B,OAAO,MAAM,CAAC,KAAK,CAAC;SACvB;KACJ;;CAEJ;AACD,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC;AAC1B,AAAO,SAAS,MAAM,CAAC,KAAK,EAAE;IAC1B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACtC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC/CM,MAAM,GAAG,SAAS,SAAS,CAAC;IAC/B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,UAAU,GAAG,IAAIA,mBAAoB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QACtE,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC;YACT,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,UAAU,CAAC,WAAW,IAAI,MAAM,EAAE;oBAClC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC;iBAC/B;gBACD,IAAI,GAAG,MAAM,IAAI,CAAC;aACrB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;YAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YAC3B,OAAO,MAAM,CAAC,KAAK,CAAC;SACvB;KACJ;;CAEJ;AACD,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC;AACpB,AAAO,SAAS,GAAG,CAAC,KAAK,EAAE;IACvB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACnC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACjDD;;;AAGA,AACA,AAAO,AACN;AACD,MAAM,KAAK,SAAS,SAAS,CAAC;IAC1B,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;QACrC,IAAI,MAAM,EAAE;YACR,IAAI,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;YAC9C,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAC/C;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAC/B,AAAC;QACD,IAAI,SAAS,KAAK,CAAC,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,iEAAiE,CAAC,CAAC;YACjF,OAAO;SACV;QACD,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,OAAO,GAAG,QAAQ,CAAC;QACvB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,OAAO,IAAI,EAAE;;gBAET,IAAI,QAAQ,KAAK,CAAC,EAAE;oBAChB,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC5B,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;oBAC3B,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;oBACzB,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC7B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;oBAC/B,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC;oBACrC,UAAU,GAAG,KAAK,CAAC;oBACnB,SAAS;iBACZ;gBACD,IAAI,yBAAyB,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC;gBACvE,IAAI,wBAAwB,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,CAAC;gBACrE,IAAI,CAAC,yBAAyB,IAAI,SAAS,MAAM,wBAAwB,IAAI,SAAS,CAAC,EAAE;oBACrF,IAAI,SAAS,GAAG,yBAAyB,GAAG,wBAAwB,GAAG,QAAQ,GAAG,SAAS,CAAC;oBAC5F,IAAI,UAAU,GAAG;wBACb,IAAI,EAAE,SAAS;wBACf,IAAI,EAAE,QAAQ,GAAG,UAAU,CAAC,IAAI,GAAG,QAAQ,GAAG,UAAU,CAAC,IAAI;wBAC7D,GAAG,EAAE,OAAO,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,GAAG,UAAU,CAAC,GAAG;wBACxD,KAAK,EAAE,SAAS,GAAG,UAAU,CAAC,KAAK,IAAI,SAAS,GAAG,SAAS,KAAK,SAAS,GAAG,SAAS,CAAC;wBACvF,MAAM,EAAE,UAAU,GAAG,UAAU,CAAC,MAAM;wBACtC,SAAS,EAAE,UAAU,CAAC,SAAS;qBAClC,CAAC;oBACF,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;oBAC3B,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC5B,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC3B,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;oBAC7B,UAAU,GAAG,CAAC,CAAC;oBACf,UAAU,GAAG,MAAM,UAAU,CAAC;iBACjC;qBACI;oBACD,QAAQ,GAAG,QAAQ,GAAG,UAAU,CAAC,IAAI,GAAG,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;oBACnE,OAAO,GAAG,OAAO,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;oBAC9D,UAAU,GAAG,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;oBAC5C,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC;oBACrC,UAAU,GAAG,KAAK,CAAC;iBACtB;aACJ;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC/B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvB,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;gBACrB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;gBACzB,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC3B,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;aACpC,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;aACtD;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,CAAC,KAAK,CAAC,+GAA+G,CAAC,CAAC;QAC/H,OAAO,IAAI,CAAC;KACf;;CAEJ;AACD,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;AACxB,AAAO,SAAS,KAAK,CAAC,KAAK,EAAE;IACzB,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACrC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACtB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACrB,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxB,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;KAC9B;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AChHD;;;AAGA,AACA,AAAO,AACN;AACD,AAAO,MAAM,UAAU,SAAS,SAAS,CAAC;IACtC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAC/B,IAAI,QAAQ,GAAG,IAAI,CAAC;QACpB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,OAAO,GAAG,QAAQ,CAAC;QACvB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,OAAO,IAAI,EAAE;gBACT,IAAI,QAAQ,KAAK,IAAI,EAAE;oBACnB,QAAQ,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,IAAI,CAAC,CAAC;oBACpD,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;oBAC3B,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;oBACzB,SAAS,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;oBACxF,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;oBACtC,aAAa,IAAI,UAAU,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;oBAC5C,UAAU,GAAG;wBACT,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,QAAQ;wBACd,GAAG,EAAE,OAAO;wBACZ,KAAK,EAAE,SAAS;wBAChB,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC;wBAC9B,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,CAAC,CAAC;qBACzC,CAAC;iBACL;qBACI;oBACD,IAAI,QAAQ,GAAG,CAAC,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;oBAC3F,IAAI,OAAO,GAAG,CAAC,QAAQ,GAAG,SAAS,IAAI,CAAC,CAAC;oBACzC,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;oBAC3D,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;oBACzD,UAAU,GAAG;wBACT,KAAK,EAAE,QAAQ;wBACf,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,OAAO;wBACb,GAAG,EAAE,MAAM;wBACX,MAAM,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC,CAAC;wBAChC,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,CAAC,CAAC;qBACzC,CAAC;oBACF,SAAS,GAAG,QAAQ,CAAC;oBACrB,QAAQ,GAAG,OAAO,CAAC;oBACnB,QAAQ,GAAG,OAAO,CAAC;oBACnB,OAAO,GAAG,MAAM,CAAC;iBACpB;gBACD,UAAU,GAAG,MAAM,UAAU,CAAC;aACjC;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC/B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvB,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvB,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;gBACrB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;gBACzB,MAAM,EAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM;gBACzD,SAAS,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,SAAS;aACxE,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;aACtD;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;QAC9C,OAAO,MAAM,CAAC;KACjB;;CAEJ;AACD,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC;AAClC,AAAO,SAAS,UAAU,CAAC,KAAK,EAAE;IAC9B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC1C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACtB,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACrB,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACvB,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QACxB,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;KAC9B;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACpGc,MAAM,iBAAiB,CAAC;IACnC,WAAW,GAAG;;;;KAIb;IACD,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE;QACnB,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1D,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACvD,OAAO,IAAI,IAAI,KAAK,CAAC;KACxB;IACD,KAAK,CAAC,IAAI,EAAE;QACR,MAAM,4BAA4B,CAAC;KACtC;IACD,kBAAkB,CAAC,IAAI,EAAE;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,sDAAsD,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAChF,OAAO,EAAE,CAAC;SACb;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;aACxC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK;YACzB,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,GAAG,KAAK,GAAG,SAAS,CAAC;SAC7D,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,KAAK;YACpB,OAAO,QAAQ,CAAC;SACnB,CAAC,CAAC;KACN;IACD,UAAU,CAAC,IAAI,EAAE;QACb,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,sDAAsD,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAChF,OAAO,KAAK,CAAC;SAChB;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;SACxB;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;QAC5B,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC;KACvE;IACD,0BAA0B,CAAC,IAAI,EAAE;QAC7B,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,aAAa,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;aACI;YACD,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,GAAG,EAAE,EAAE;gBACP,KAAK,EAAE,EAAE;aACZ,CAAC;YACF,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC;YAC9C,OAAO,CAAC,GAAG,aAAa,EAAE;gBACtB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,EAAE,CAAC;aACP;YACD,OAAO,SAAS,CAAC;SACpB;KACJ;IACD,2BAA2B,CAAC,IAAI,EAAE;QAC9B,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACvC,IAAI,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,WAAW,EAAE,KAAK,EAAE;YAC7D,IAAI,CAAC,GAAG,CAAC,CAAC;YACV,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAE,EAAE;gBACR,IAAI,EAAE,EAAE;gBACR,GAAG,EAAE,EAAE;gBACP,KAAK,EAAE,EAAE;aACZ,CAAC;YACF,OAAO,CAAC,GAAG,aAAa,EAAE;gBACtB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1C,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,EAAE,CAAC;aACP;YACD,OAAO,SAAS,CAAC;SACpB,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,EAAE,QAAQ,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;QACtF,OAAO,aAAa,CAAC;KACxB;CACJ;;AC3Fc,MAAM,WAAW,SAAS,iBAAiB,CAAC;IACvD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,iBAAiB,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,iBAAiB,IAAI,CAAC,YAAY,GAAG,aAAa;aACjD,YAAY,GAAG,cAAc,CAAC,CAAC,CAAC;QACrC,IAAI,cAAc,GAAG,aAAa,GAAG,cAAc,CAAC;QACpD,IAAI,SAAS,IAAI,CAAC,cAAc,GAAG,YAAY;aAC1C,aAAa,GAAG,YAAY,CAAC;aAC7B,aAAa,GAAG,cAAc,CAAC;aAC/B,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC;QACvC,IAAI,2BAA2B,GAAG,cAAc,GAAG,iBAAiB,CAAC;QACrE,QAAQ,cAAc,IAAI,iBAAiB,IAAI,SAAS,IAAI,cAAc,IAAI,2BAA2B,EAAE;KAC9G;CACJ;AACD,AAAO,SAAS,WAAW,CAAC,IAAI,EAAE;IAC9B,OAAO,IAAI,WAAW,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC7C;;AClCc,MAAM,uBAAuB,SAAS,iBAAiB,CAAC;IACnE,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,kBAAkB,IAAI,CAAC,cAAc,GAAG,aAAa;aACpD,aAAa,GAAG,cAAc,CAAC;aAC/B,cAAc,GAAG,cAAc,CAAC;aAChC,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC;QACvC,QAAQ,kBAAkB,EAAE;KAC/B;CACJ;AACD,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE;IAC1C,OAAO,IAAI,uBAAuB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACzD;;ACxBc,MAAM,aAAa,SAAS,iBAAiB,CAAC;IACzD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;KAC/B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,sBAAsB,IAAI,CAAC,aAAa,GAAG,cAAc;aACxD,cAAc,GAAG,cAAc,CAAC;aAChC,cAAc,GAAG,eAAe,CAAC;aACjC,aAAa,GAAG,aAAa,CAAC;aAC9B,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC;QACtC,QAAQ,sBAAsB,EAAE;KACnC;CACJ;AACD,AAAO,SAAS,aAAa,CAAC,IAAI,EAAE;IAChC,OAAO,IAAI,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC/C;;ACzBc,MAAM,kBAAkB,SAAS,iBAAiB,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;KACpC;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,2BAA2B,IAAI,CAAC,aAAa,GAAG,cAAc;aAC7D,cAAc,GAAG,cAAc,CAAC;aAChC,cAAc,GAAG,eAAe,CAAC;aACjC,aAAa,GAAG,aAAa,CAAC;aAC9B,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC;QACtC,IAAI,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;QAC7E,QAAQ,2BAA2B,IAAI,eAAe,EAAE;KAC3D;CACJ;AACD,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE;IACrC,OAAO,IAAI,kBAAkB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACpD;;AC1Bc,MAAM,IAAI,SAAS,iBAAiB,CAAC;IAChD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QACnB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACnE,IAAI,gBAAgB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtF,IAAI,gBAAgB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtF,QAAQ,iBAAiB,IAAI,gBAAgB,IAAI,gBAAgB,EAAE;KACtE;CACJ;AACD,AAAO,SAAS,IAAI,CAAC,IAAI,EAAE;IACvB,OAAO,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACtC;;AClBc,MAAM,eAAe,SAAS,iBAAiB,CAAC;IAC3D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,iBAAiB,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC;YACnC,MAAM,EAAE,CAAC,cAAc,CAAC;YACxB,OAAO,EAAE,CAAC,eAAe,CAAC;YAC1B,MAAM,EAAE,CAAC,cAAc,CAAC;YACxB,KAAK,EAAE,CAAC,aAAa,CAAC;SACzB,CAAC,CAAC;QACH,IAAI,cAAc,GAAG,aAAa,GAAG,cAAc,CAAC;QACpD,IAAI,SAAS,IAAI,CAAC,cAAc,GAAG,YAAY;aAC1C,aAAa,GAAG,YAAY,CAAC;aAC7B,aAAa,GAAG,cAAc,CAAC;aAC/B,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC;QACvC,IAAI,2BAA2B,GAAG,cAAc,GAAG,iBAAiB,CAAC;QACrE,QAAQ,cAAc,IAAI,UAAU,IAAI,cAAc,IAAI,SAAS;YAC/D,2BAA2B,EAAE;KACpC;CACJ;AACD,AAAO,SAAS,eAAe,CAAC,IAAI,EAAE;IAClC,OAAO,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACjD;;ACxCc,MAAM,iBAAiB,SAAS,iBAAiB,CAAC;IAC7D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAC;KACnC;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,eAAe,GAAG,eAAe,GAAG,cAAc,CAAC;QACvD,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,gBAAgB,GAAG,cAAc,GAAG,YAAY,CAAC;QACrD,IAAI,mBAAmB,IAAI,CAAC,cAAc,GAAG,aAAa;aACrD,eAAe,GAAG,aAAa,CAAC;aAChC,cAAc,GAAG,cAAc,CAAC;aAChC,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC;QACvC,QAAQ,cAAc,IAAI,eAAe,IAAI,cAAc,IAAI,gBAAgB,IAAI,mBAAmB,EAAE;KAC3G;CACJ;AACD,AAAO,SAAS,iBAAiB,CAAC,IAAI,EAAE;IACpC,OAAO,IAAI,iBAAiB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACnD;;AChCc,MAAM,eAAe,SAAS,iBAAiB,CAAC;IAC3D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC;YAC7D,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC;YACxC,QAAQ,GAAG,SAAS;YACpB,QAAQ,GAAG,QAAQ,CAAC;QACxB,QAAQ,gBAAgB,EAAE;KAC7B;CACJ;AACD,AAAO,SAAS,eAAe,CAAC,IAAI,EAAE;IAClC,OAAO,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACjD;;ACpBc,MAAM,YAAY,SAAS,iBAAiB,CAAC;IACxD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;KAC9B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,iBAAiB,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,WAAW,GAAG,aAAa,GAAG,YAAY,CAAC;QAC/C,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,eAAe,GAAG,eAAe,GAAG,cAAc,CAAC;QACvD,IAAI,qBAAqB,IAAI,CAAC,YAAY,GAAG,cAAc;aACtD,eAAe,GAAG,iBAAiB,CAAC,CAAC,CAAC;QAC3C,QAAQ,WAAW,IAAI,cAAc,IAAI,qBAAqB,IAAI,eAAe,EAAE;KACtF;CACJ;AACD,AAAO,SAAS,YAAY,CAAC,IAAI,EAAE;IAC/B,OAAO,IAAI,YAAY,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC9C;;AC1Bc,MAAM,kBAAkB,SAAS,iBAAiB,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,SAAS,GAAG,cAAc,GAAG,aAAa;YAC1C,aAAa,GAAG,cAAc,CAAC;QACnC,IAAI,YAAY,GAAG,aAAa,GAAG,cAAc;YAC7C,cAAc,GAAG,eAAe;YAChC,aAAa,GAAG,cAAc,CAAC;QACnC,IAAI,0BAA0B,GAAG,cAAc,GAAG,cAAc;YAC5D,cAAc,GAAG,aAAa;YAC9B,cAAc,GAAG,aAAa;YAC9B,aAAa,GAAG,eAAe,CAAC;QACpC,QAAQ,SAAS,IAAI,YAAY,IAAI,0BAA0B,EAAE;KACpE;CACJ;AACD,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE;IACrC,OAAO,IAAI,kBAAkB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACpD;;ACjCc,MAAM,kBAAkB,SAAS,iBAAiB,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,eAAe,GAAG,SAAS,GAAG,QAAQ,CAAC;QAC3C,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChF,eAAe,GAAG,eAAe,IAAI,CAAC,SAAS,GAAG,QAAQ,KAAK,CAAC,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC;QACxF,OAAO,eAAe,CAAC;KAC1B;CACJ;AACD,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE;IACrC,OAAO,IAAI,kBAAkB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACpD;;ACnBc,MAAM,0BAA0B,SAAS,iBAAiB,CAAC;IACtE,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,uBAAuB,GAAG,SAAS,GAAG,QAAQ,CAAC;QACnD,uBAAuB,GAAG,uBAAuB,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC9F,uBAAuB,GAAG,uBAAuB,IAAI,CAAC,SAAS,GAAG,QAAQ,KAAK,CAAC,IAAI,QAAQ,GAAG,SAAS,CAAC,CAAC;QAC1G,OAAO,uBAAuB,CAAC;KAClC;CACJ;AACD,AAAO,SAAS,0BAA0B,CAAC,IAAI,EAAE;IAC7C,OAAO,IAAI,0BAA0B,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC5D;;ACnBc,MAAM,kBAAkB,SAAS,iBAAiB,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,eAAe,GAAG,QAAQ,GAAG,SAAS,CAAC;QAC3C,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAC/E,eAAe,GAAG,eAAe,IAAI,CAAC,QAAQ,GAAG,SAAS,KAAK,CAAC,IAAI,SAAS,GAAG,OAAO,CAAC,CAAC;QACzF,OAAO,eAAe,CAAC;KAC1B;CACJ;AACD,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE;IACrC,OAAO,IAAI,kBAAkB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACpD;;ACnBc,MAAM,0BAA0B,SAAS,iBAAiB,CAAC;IACtE,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,uBAAuB,GAAG,QAAQ,GAAG,SAAS,CAAC;QACnD,uBAAuB,GAAG,uBAAuB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/F,uBAAuB,GAAG,uBAAuB,IAAI,CAAC,QAAQ,GAAG,SAAS,KAAK,CAAC,IAAI,QAAQ,GAAG,QAAQ,CAAC,CAAC;QACzG,OAAO,uBAAuB,CAAC;KAClC;CACJ;AACD,AAAO,SAAS,0BAA0B,CAAC,IAAI,EAAE;IAC7C,OAAO,IAAI,0BAA0B,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC5D;;ACbc,MAAM,aAAa,SAAS,iBAAiB,CAAC;IACzD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzC,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACnD,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,OAAO,SAAS,CAAC;KACpB;IACD,aAAa,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE;QAChC,IAAI,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;;QAE1B,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/E,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;;QAEhF,OAAO,MAAM,GAAG,KAAK,CAAC;KACzB;IACD,cAAc,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE;QACjC,IAAI,KAAK,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC;QAClC,IAAI,kBAAkB,GAAG;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YACnC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;SACpC,CAAC;QACF,IAAI,SAAS,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;QACvD,SAAS,GAAG,SAAS,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;QACxE,SAAS,GAAG,SAAS,IAAI,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;QAChE,SAAS,GAAG,SAAS,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;QACxE,OAAO,SAAS,CAAC;KACpB;IACD,eAAe,CAAC,IAAI,EAAE;QAClB,IAAI,cAAc,GAAG;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACpB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACrB,CAAC;QACF,IAAI,oBAAoB,GAAG;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACpB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACrB,CAAC;;QAEF,IAAI,SAAS,GAAG,oBAAoB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC;QACvE,OAAO,SAAS,IAAI,cAAc,CAAC,KAAK,GAAG,oBAAoB,CAAC,KAAK,CAAC;KACzE;CACJ;AACD,AAAO,SAAS,aAAa,CAAC,IAAI,EAAE;IAChC,OAAO,IAAI,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC/C;;AC7Dc,MAAM,wBAAwB,SAAS,aAAa,CAAC;IAChE,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;KAC1C;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAChD,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,SAAS,CAAC;KACpB;CACJ;AACD,AAAO,SAAS,wBAAwB,CAAC,IAAI,EAAE;IAC3C,OAAO,IAAI,wBAAwB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC1D;;ACXc,MAAM,aAAa,SAAS,iBAAiB,CAAC;IACzD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KACjE;IACD,aAAa,CAAC,IAAI,EAAE;;QAEhB,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QACvE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;;QAExE,OAAO,MAAM,GAAG,KAAK,CAAC;KACzB;CACJ;AACD,AAAO,SAAS,aAAa,CAAC,IAAI,EAAE;IAChC,OAAO,IAAI,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC/C;;ACPD,IAAI,eAAe,GAAG;IAClB,IAAI,uBAAuB,EAAE;IAC7B,IAAI,iBAAiB,EAAE;IACvB,IAAI,aAAa,EAAE;IACnB,IAAI,kBAAkB,EAAE;IACxB,IAAI,eAAe,EAAE;IACrB,IAAI,WAAW,EAAE;IACjB,IAAI,eAAe,EAAE;IACrB,IAAI,YAAY,EAAE;IAClB,IAAI,kBAAkB,EAAE;IACxB,IAAI,kBAAkB,EAAE;IACxB,IAAI,0BAA0B,EAAE;IAChC,IAAI,aAAa,EAAE;IACnB,IAAI,wBAAwB,EAAE;IAC9B,IAAI,aAAa,EAAE;CACtB,CAAC;AACF,AAAe,MAAM,eAAe,SAAS,iBAAiB,CAAC;IAC3D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;KACtC;IACD,UAAU,CAAC,IAAI,EAAE;QACb,OAAO,eAAe,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,OAAO,EAAE;YACpD,IAAI,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACtC,OAAO,KAAK,IAAI,MAAM,CAAC;SAC1B,EAAE,KAAK,CAAC,CAAC;KACb;CACJ;AACD,AAAO,SAAS,OAAO,CAAC,IAAI,EAAE;IAC1B,OAAO,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACjD;;AC5Cc,MAAM,uBAAuB,SAAS,iBAAiB,CAAC;IACnE,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,kBAAkB,IAAI,CAAC,cAAc,GAAG,aAAa;aACpD,aAAa,GAAG,cAAc,CAAC;aAC/B,cAAc,GAAG,cAAc,CAAC;aAChC,aAAa,GAAG,eAAe,CAAC,CAAC,CAAC;QACvC,QAAQ,kBAAkB,EAAE;KAC/B;CACJ;AACD,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE;IAC1C,OAAO,IAAI,uBAAuB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACzD;;ACxBc,MAAM,aAAa,SAAS,iBAAiB,CAAC;IACzD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;KAC/B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,sBAAsB,IAAI,CAAC,aAAa,GAAG,cAAc;aACxD,cAAc,GAAG,cAAc,CAAC;aAChC,cAAc,GAAG,eAAe,CAAC;aACjC,aAAa,GAAG,aAAa,CAAC;aAC9B,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC;QACtC,QAAQ,sBAAsB,EAAE;KACnC;CACJ;AACD,AAAO,SAAS,aAAa,CAAC,IAAI,EAAE;IAChC,OAAO,IAAI,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC/C;;ACzBc,MAAM,kBAAkB,SAAS,iBAAiB,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;KACpC;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,2BAA2B,IAAI,CAAC,aAAa,GAAG,cAAc;aAC7D,cAAc,GAAG,cAAc,CAAC;aAChC,cAAc,GAAG,eAAe,CAAC;aACjC,aAAa,GAAG,aAAa,CAAC;aAC9B,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC;QACtC,IAAI,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;QAC7E,QAAQ,2BAA2B,IAAI,eAAe,EAAE;KAC3D;CACJ;AACD,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE;IACrC,OAAO,IAAI,kBAAkB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACpD;;ACzBc,MAAM,eAAe,SAAS,iBAAiB,CAAC;IAC3D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,iBAAiB,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC;YACnC,MAAM,EAAE,CAAC,cAAc,CAAC;YACxB,OAAO,EAAE,CAAC,eAAe,CAAC;YAC1B,MAAM,EAAE,CAAC,cAAc,CAAC;YACxB,KAAK,EAAE,CAAC,aAAa,CAAC;SACzB,CAAC,CAAC;QACH,IAAI,cAAc,GAAG,aAAa,GAAG,cAAc,CAAC;QACpD,IAAI,SAAS,IAAI,CAAC,cAAc,GAAG,aAAa;aAC3C,aAAa,GAAG,aAAa,CAAC;aAC9B,aAAa,GAAG,aAAa,CAAC;aAC9B,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC;QACvC,IAAI,2BAA2B,GAAG,cAAc,GAAG,iBAAiB,CAAC;QACrE,QAAQ,cAAc,IAAI,UAAU,IAAI,SAAS,IAAI,cAAc,IAAI,2BAA2B,EAAE;KACvG;CACJ;AACD,AAAO,SAAS,eAAe,CAAC,IAAI,EAAE;IAClC,OAAO,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACjD;;ACvCc,MAAM,WAAW,SAAS,iBAAiB,CAAC;IACvD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;QAC1B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,iBAAiB,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC;QAC/D,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,iBAAiB,IAAI,CAAC,aAAa,GAAG,aAAa;aAClD,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC;QACtC,IAAI,cAAc,GAAG,aAAa,GAAG,cAAc,CAAC;QACpD,IAAI,SAAS,IAAI,CAAC,cAAc,GAAG,aAAa;aAC3C,aAAa,GAAG,aAAa,CAAC;aAC9B,aAAa,GAAG,aAAa,CAAC;aAC9B,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC;QACvC,IAAI,2BAA2B,GAAG,cAAc,GAAG,iBAAiB,CAAC;QACrE,QAAQ,cAAc,IAAI,iBAAiB,IAAI,SAAS,IAAI,cAAc,IAAI,2BAA2B,EAAE;KAC9G;CACJ;AACD,AAAO,SAAS,WAAW,CAAC,IAAI,EAAE;IAC9B,OAAO,IAAI,WAAW,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC7C;;AClCc,MAAM,eAAe,SAAS,iBAAiB,CAAC;IAC3D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAC5D,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC;YACzC,QAAQ,GAAG,SAAS;YACpB,QAAQ,GAAG,OAAO,CAAC;QACvB,QAAQ,gBAAgB,EAAE;KAC7B;CACJ;AACD,AAAO,SAAS,eAAe,CAAC,IAAI,EAAE;IAClC,OAAO,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACjD;;ACpBc,MAAM,eAAe,SAAS,iBAAiB,CAAC;IAC3D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;QAC9B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,WAAW,GAAG,YAAY,GAAG,aAAa;YAC1C,aAAa,GAAG,YAAY,CAAC;QACjC,IAAI,YAAY,GAAG,aAAa,GAAG,cAAc;YAC7C,cAAc,GAAG,eAAe;YAChC,aAAa,GAAG,cAAc,CAAC;QACnC,IAAI,0BAA0B,GAAG,aAAa,GAAG,cAAc;YAC3D,cAAc,GAAG,cAAc;YAC/B,cAAc,GAAG,aAAa;YAC9B,aAAa,GAAG,eAAe,CAAC;QACpC,QAAQ,WAAW,IAAI,YAAY,IAAI,0BAA0B,EAAE;KACtE;CACJ;AACD,AAAO,SAAS,eAAe,CAAC,IAAI,EAAE;IAClC,OAAO,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACjD;;AC7Bc,MAAM,UAAU,SAAS,iBAAiB,CAAC;IACtD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvC,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACnD,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,OAAO,SAAS,CAAC;KACpB;IACD,WAAW,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE;QAC9B,IAAI,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;;QAE1B,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/E,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;;QAEhF,OAAO,KAAK,GAAG,MAAM,CAAC;KACzB;IACD,cAAc,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE;QACjC,IAAI,KAAK,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC;QAClC,IAAI,kBAAkB,GAAG;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YACnC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;SACpC,CAAC;QACF,IAAI,SAAS,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;QACvD,SAAS,GAAG,SAAS,IAAI,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;QAChE,OAAO,SAAS,CAAC;KACpB;IACD,eAAe,CAAC,IAAI,EAAE;QAClB,IAAI,cAAc,GAAG;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACpB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACrB,CAAC;QACF,IAAI,oBAAoB,GAAG;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACpB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACrB,CAAC;;QAEF,IAAI,SAAS,GAAG,oBAAoB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC;QACvE,OAAO,SAAS,IAAI,cAAc,CAAC,KAAK,GAAG,oBAAoB,CAAC,KAAK,CAAC;KACzE;CACJ;AACD,AAAO,SAAS,UAAU,CAAC,IAAI,EAAE;IAC7B,OAAO,IAAI,UAAU,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC5C;;ACzDc,MAAM,qBAAqB,SAAS,UAAU,CAAC;IAC1D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;KACvC;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9C,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,SAAS,CAAC;KACpB;CACJ;AACD,AAAO,SAAS,qBAAqB,CAAC,IAAI,EAAE;IACxC,OAAO,IAAI,qBAAqB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACvD;;ACTc,MAAM,YAAY,SAAS,iBAAiB,CAAC;IACxD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACvC,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACnD,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACpD,OAAO,SAAS,CAAC;KACpB;IACD,WAAW,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE;QAC9B,IAAI,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;;QAE1B,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/E,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;;QAEhF,OAAO,KAAK,GAAG,MAAM,CAAC;KACzB;IACD,cAAc,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE;QACjC,IAAI,KAAK,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC;QAClC,IAAI,kBAAkB,GAAG;YACrB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YACnC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;YAC/B,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC;SACpC,CAAC;QACF,IAAI,SAAS,GAAG,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;QAC/D,SAAS,GAAG,SAAS,IAAI,0BAA0B,CAAC,kBAAkB,CAAC,CAAC;QACxE,OAAO,SAAS,CAAC;KACpB;IACD,eAAe,CAAC,IAAI,EAAE;QAClB,IAAI,cAAc,GAAG;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACpB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACrB,CAAC;QACF,IAAI,oBAAoB,GAAG;YACvB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACpB,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAChB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACrB,CAAC;;QAEF,IAAI,SAAS,GAAG,oBAAoB,CAAC,IAAI,GAAG,oBAAoB,CAAC,KAAK,CAAC;QACvE,OAAO,SAAS,IAAI,cAAc,CAAC,KAAK,GAAG,oBAAoB,CAAC,KAAK,CAAC;KACzE;CACJ;AACD,AAAO,SAAS,YAAY,CAAC,IAAI,EAAE;IAC/B,OAAO,IAAI,YAAY,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC9C;;ACzDc,MAAM,uBAAuB,SAAS,YAAY,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;KACzC;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9C,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,SAAS,CAAC;KACpB;CACJ;AACD,AAAO,SAAS,uBAAuB,CAAC,IAAI,EAAE;IAC1C,OAAO,IAAI,uBAAuB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACzD;;ACXc,MAAM,UAAU,SAAS,iBAAiB,CAAC;IACtD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjE;IACD,WAAW,CAAC,IAAI,EAAE;;QAEd,IAAI,KAAK,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;QACvE,IAAI,MAAM,GAAG,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;;QAExE,OAAO,KAAK,GAAG,MAAM,CAAC;KACzB;CACJ;AACD,AAAO,SAAS,UAAU,CAAC,IAAI,EAAE;IAC7B,OAAO,IAAI,UAAU,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC5C;;ACPD,IAAI,eAAe,GAAG;IAClB,IAAI,uBAAuB,EAAE;IAC7B,IAAI,aAAa,EAAE;IACnB,IAAI,kBAAkB,EAAE;IACxB,IAAI,eAAe,EAAE;IACrB,IAAI,WAAW,EAAE;IACjB,IAAI,eAAe,EAAE;IACrB,IAAI,eAAe,EAAE;IACrB,IAAI,kBAAkB,EAAE;IACxB,IAAI,0BAA0B,EAAE;IAChC,IAAI,UAAU,EAAE;IAChB,IAAI,qBAAqB,EAAE;IAC3B,IAAI,YAAY,EAAE;IAClB,IAAI,uBAAuB,EAAE;IAC7B,IAAI,UAAU,EAAE;CACnB,CAAC;AACF,AAAe,MAAM,eAAe,SAAS,iBAAiB,CAAC;IAC3D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;KACtC;IACD,UAAU,CAAC,IAAI,EAAE;QACb,OAAO,eAAe,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,OAAO,EAAE;YACpD,OAAO,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SAC5C,EAAE,KAAK,CAAC,CAAC;KACb;CACJ;AACD,AAAO,SAAS,OAAO,CAAC,IAAI,EAAE;IAC1B,OAAO,IAAI,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACjD;;AC1Cc,MAAM,aAAa,SAAS,iBAAiB,CAAC;IACzD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC;YACnC,MAAM,EAAE,CAAC,cAAc,CAAC;YACxB,OAAO,EAAE,CAAC,eAAe,CAAC;YAC1B,MAAM,EAAE,CAAC,cAAc,CAAC;YACxB,KAAK,EAAE,CAAC,aAAa,CAAC;SACzB,CAAC,CAAC;QACH,IAAI,SAAS,IAAI,CAAC,cAAc,GAAG,YAAY;aAC1C,YAAY,GAAG,cAAc,CAAC;aAC9B,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC;QACtC,IAAI,cAAc,IAAI,aAAa,GAAG,aAAa,CAAC,CAAC;QACrD,QAAQ,cAAc,IAAI,UAAU,IAAI,SAAS,IAAI,cAAc,EAAE;KACxE;CACJ;AACD,AAAO,SAAS,aAAa,CAAC,IAAI,EAAE;IAChC,OAAO,IAAI,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC/C;;ACpCc,MAAM,cAAc,SAAS,iBAAiB,CAAC;IAC1D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACnC,IAAI,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,gBAAgB,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,CAAC,CAAC,CAAC;QAC9D,IAAI,cAAc,GAAG,cAAc,GAAG,aAAa,CAAC;QACpD,IAAI,eAAe,GAAG,eAAe,GAAG,cAAc,CAAC;QACvD,IAAI,kBAAkB,IAAI,CAAC,cAAc,GAAG,aAAa;aACpD,eAAe,GAAG,gBAAgB,CAAC;aACnC,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC;QACvC,QAAQ,cAAc,IAAI,eAAe,IAAI,kBAAkB,EAAE;KACpE;CACJ;AACD,AAAO,SAAS,cAAc,CAAC,IAAI,EAAE;IACjC,OAAO,IAAI,cAAc,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAChD;;AC1Bc,MAAM,aAAa,SAAS,iBAAiB,CAAC;IACzD,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;KAC/B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACnE,IAAI,gBAAgB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtF,IAAI,gBAAgB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtF,QAAQ,iBAAiB,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,EAAE;KACvE;CACJ;AACD,AAAO,SAAS,aAAa,CAAC,IAAI,EAAE;IAChC,OAAO,IAAI,aAAa,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC/C;;ACnBc,MAAM,cAAc,SAAS,iBAAiB,CAAC;IAC1D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;KAChC;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QACnE,IAAI,gBAAgB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACtF,IAAI,gBAAgB,GAAG,iBAAiB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACtF,QAAQ,iBAAiB,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,EAAE;KACvE;CACJ;AACD,AAAO,SAAS,cAAc,CAAC,IAAI,EAAE;IACjC,OAAO,IAAI,cAAc,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAChD;;ACnBc,MAAM,kBAAkB,SAAS,iBAAiB,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;QAChD,IAAI,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;QACvD,IAAI,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QACrD,IAAI,oBAAoB,GAAG,UAAU,GAAG,iBAAiB;YACrD,UAAU,GAAG,iBAAiB,CAAC;QACnC,OAAO,oBAAoB,CAAC;KAC/B;CACJ;AACD,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE;IACrC,OAAO,IAAI,kBAAkB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACpD;;ACrBc,MAAM,kBAAkB,SAAS,iBAAiB,CAAC;IAC9D,WAAW,GAAG;QACV,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;KAC1B;IACD,KAAK,CAAC,IAAI,EAAE;QACR,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC;QAChD,IAAI,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;QACtD,IAAI,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC;QACrD,IAAI,oBAAoB,GAAG,UAAU,GAAG,iBAAiB;YACrD,UAAU,GAAG,iBAAiB,CAAC;QACnC,OAAO,oBAAoB,CAAC;KAC/B;CACJ;AACD,AAAO,SAAS,kBAAkB,CAAC,IAAI,EAAE;IACrC,OAAO,IAAI,kBAAkB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CACpD;;ACtBD;;;;;;;;;;;;AAYA,AAAO,SAAS,oBAAoB,CAAC,KAAK,EAAE,GAAG,EAAE;IAC7C,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAC9E,IAAI,YAAY,CAAC;IACjB,IAAI,KAAK,GAAG,GAAG,EAAE;QACb,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE;YACvC,IAAI,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC;YAC7D,OAAO,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;SAC1C,CAAC,CAAC;KACN;SACI;QACD,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE;YACvC,IAAI,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC;YAC7D,OAAO,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC;SAC1C,CAAC,CAAC;KACN;IACD,OAAO,YAAY,CAAC;CACvB;;ACfM,MAAM,aAAa,SAAS,SAAS,CAAC;IACzC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,QAAQ,GAAG;YACX,gBAAgB,EAAE,CAAC;YACnB,UAAU,EAAE,EAAE;YACd,UAAU,EAAE,EAAE;YACd,YAAY,EAAE,EAAE;SACnB,CAAC;QACF,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAChD,IAAI,qBAAqB,GAAG,IAAIN,mBAAU,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3F,IAAI,eAAe,GAAG,IAAIA,mBAAU,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC/E,IAAI,cAAc,GAAG,IAAIA,mBAAU,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9E,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,CAAC;YACT,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;YAC1G,IAAI,aAAa,GAAG,CAAC,CAAC;YACtB,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;;gBAET,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACrC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC/B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI,aAAa,GAAG,MAAM,EAAE;oBACxB,aAAa,EAAE,CAAC;iBACnB;qBACI;;oBAED,IAAI,cAAc,GAAG,CAAC,qBAAqB,CAAC,UAAU,GAAG,qBAAqB,CAAC,SAAS,IAAI,CAAC,CAAC;;oBAE9F,IAAI,QAAQ,GAAG,CAAC,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,SAAS,IAAI,CAAC,CAAC;;oBAE5E,IAAI,KAAK,GAAG,CAAC,cAAc,GAAG,QAAQ,IAAI,CAAC,CAAC;;oBAE5C,IAAI,KAAK,GAAG,CAAC,cAAc,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS,IAAI,CAAC,CAAC;;;;;;;;oBAQvE,MAAM,GAAG;wBACL,UAAU,EAAE,cAAc;wBAC1B,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,KAAK;wBACZ,KAAK,EAAE,KAAK;qBACf,CAAC;iBACL;gBACD,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;YAC/B,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvB,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;aACxB,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,EAAE;gBACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,SAAS,CAAC,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;KAC3C;CACJ;AACD,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC;AACxC,AAAO,SAAS,aAAa,CAAC,KAAK,EAAE;IACjC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC7C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AC3FM,MAAM,oBAAoB,SAAS,cAAc,CAAC;IACrD,WAAW,GAAG;QACV,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;KACvB;CACJ;AACD,AAAO,MAAM,qBAAqB,SAAS,cAAc,CAAC;CACzD;AACD,AAAC;AACD,AAAO,MAAM,eAAe,SAAS,SAAS,CAAC;IAC3C,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;QACtC,IAAI,UAAU,GAAG,IAAI,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAClG,IAAI,WAAW,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnH,IAAI,IAAI,CAAC;QACT,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,qBAAqB,CAAC;YAC1B,IAAI,MAAM,CAAC;YACX,IAAI,GAAG,KAAK,CAAC;YACb,OAAO,IAAI,EAAE;gBACT,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;gBACrB,IAAI,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACrC,IAAIO,MAAG,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACtC,IAAI,EAAE,IAAI,SAAS,IAAIA,MAAG,IAAI,SAAS,EAAE;oBACrC,MAAM,GAAG;wBACL,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,EAAE,IAAI,KAAK,CAAC,UAAU,IAAIA,MAAG,CAAC,CAAC;wBACtC,KAAK,EAAE,EAAE,IAAI,KAAK,CAAC,UAAU,IAAIA,MAAG,CAAC,CAAC;qBACzC,CAAC;iBACL;gBACD,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,KAAK;YAC/B,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;gBACrB,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;aAC5B,CAAC;YACF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YAC3B,OAAO,MAAM,CAAC,KAAK,CAAC;SACvB;KACJ;;CAEJ;AACD,eAAe,CAAC,SAAS,GAAG,eAAe,CAAC;AAC5C,AAAO,SAAS,eAAe,CAAC,KAAK,EAAE;IACnC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC/C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;ACxEM,MAAM,mBAAmB,SAAS,cAAc,CAAC;IACpD,WAAW,GAAG;QACV,KAAK,CAAC,GAAG,SAAS,CAAC,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;KACvB;CACJ;AACD,AAAO,MAAM,oBAAoB,SAAS,cAAc,CAAC;CACxD;AACD,AAAC;AACD,AAAO,MAAM,cAAc,SAAS,SAAS,CAAC;IAC1C,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACvB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,WAAW,GAAG,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChH,IAAI,aAAa,GAAG,IAAIP,mBAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACxE,IAAI,CAAC,SAAS,GAAG,CAAC,aAAa;YAC3B,IAAI,MAAM,CAAC;YACX,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAIO,MAAG,CAAC;YACR,OAAO,IAAI,EAAE;gBACT,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACxBA,MAAG,GAAG,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAClC,IAAI,CAAC,aAAa,CAAC,WAAW,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAKA,MAAG,IAAI,SAAS,EAAE;oBACvE,MAAM,GAAG;wBACL,QAAQ,EAAE,aAAa,CAAC,UAAU,GAAGA,MAAG,GAAG,KAAK,CAAC,UAAU;wBAC3D,SAAS,EAAE,aAAa,CAAC,SAAS,GAAGA,MAAG,GAAG,KAAK,CAAC,UAAU;qBAC9D,CAAC;iBACL;gBACD,IAAI,GAAG,MAAM,MAAM,CAAC;aACvB;SACJ,GAAG,CAAC;QACL,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,KAAK;YAC/B,IAAI,SAAS,GAAG;gBACZ,IAAI,EAAE,QAAQ;gBACd,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC;gBAChB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC;aACvB,CAAC;YACF,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;;IAED,SAAS,CAAC,KAAK,EAAE;QACb,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YAC3B,OAAO,MAAM,CAAC,KAAK,CAAC;SACvB;KACJ;;CAEJ;AACD,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC;AAC1C,AAAO,SAAS,cAAc,CAAC,KAAK,EAAE;IAClC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC9C,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB,AACD,AAAC;;AChEM,MAAM,OAAO,SAAS,SAAS,CAAC;IACnC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,MAAM,KAAK,IAAI,aAAa;YACxB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,OAAO,IAAI,EAAE;gBACT,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrC,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBACzC,IAAI,OAAO,GAAG,CAAC,CAAC;gBAChB,OAAO,MAAM,KAAK,IAAI,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE;oBACtE,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,EAAE;wBAC/C,MAAM,GAAG,KAAK,CAAC;qBAClB;yBACI,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,EAAE;wBACpD,MAAM,GAAG,IAAI,CAAC;qBACjB;yBACI,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,EAAE;wBACtD,OAAO,IAAI,CAAC,CAAC;qBAChB;iBACJ;gBACD,IAAI,MAAM,KAAK,IAAI,EAAE;oBACjB,YAAY,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAChC,YAAY,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACnC;gBACD,OAAO,GAAG,MAAM,MAAM,CAAC;aAC1B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;YACjC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBACzB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;aAC5B,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,OAAO,aAAa,CAAC,KAAK,EAAE;QACxB,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAChD,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;SACnD;KACJ;IACD,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACvB,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,MAAM;SACjB,CAAC,CAAC,KAAK,CAAC;KACZ;;CAEJ;AACD,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC;AAC5B,AAAO,SAAS,OAAO,CAAC,KAAK,EAAE;IAC3B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACvC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB;;ACrEM,MAAM,SAAS,SAAS,SAAS,CAAC;IACrC,WAAW,CAAC,KAAK,EAAE;QACf,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACzB,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,MAAM,KAAK,IAAI,aAAa;YACxB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,CAAC;YACnB,OAAO,IAAI,EAAE;gBACT,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACrC,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBACzC,IAAI,OAAO,GAAG,CAAC,CAAC;gBAChB,OAAO,MAAM,KAAK,IAAI,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,EAAE;oBACtE,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,EAAE;wBAC/C,MAAM,GAAG,KAAK,CAAC;qBAClB;yBACI,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,EAAE;wBACpD,MAAM,GAAG,IAAI,CAAC;qBACjB;yBACI,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,OAAO,CAAC,EAAE;wBACtD,OAAO,IAAI,CAAC,CAAC;qBAChB;iBACJ;gBACD,IAAI,MAAM,KAAK,IAAI,EAAE;oBACjB,YAAY,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBAChC,YAAY,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;iBACnC;gBACD,OAAO,GAAG,MAAM,MAAM,CAAC;aAC1B;SACJ,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;YACjC,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAC7B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBACzB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;aAC5B,CAAC,CAAC;YACH,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aAClC;SACJ,CAAC,CAAC;KACN;IACD,OAAO,aAAa,CAAC,KAAK,EAAE;QACxB,IAAI,KAAK,CAAC,aAAa,EAAE;YACrB,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;YAChD,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC;SACnD;KACJ;IACD,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACvB,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,MAAM;SACjB,CAAC,CAAC,KAAK,CAAC;KACZ;;CAEJ;AACD,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC;AAChC,AAAO,SAAS,SAAS,CAAC,KAAK,EAAE;IAC7B,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,MAAM,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IACzC,IAAI,KAAK,CAAC,aAAa,EAAE;QACrB,MAAM,CAAC,OAAO,EAAE,CAAC;KACpB;IACD,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;CACjB;;AC5EM,SAAS,sBAAsB,IAAI;EACxC,IAAI,mBAAmB,KAAK,EAAE,CAAA;EAC9B,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACjC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACjC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;EAC3C,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EACtC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACjC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EACvC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EACtC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;EAEjC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;EAC9C,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EACvC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACjC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EAC1C,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAClC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;EAEvC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EAC1C,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;EAEhC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACxC,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACxC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACpC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EACnC,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EAChC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;EAChD,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/B,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACpC,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACpC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EAC1C,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;EACjC,mBAAmB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;EACpD,mBAAmB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;EACpD,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;EAC3C,mBAAmB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;EAC9C,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EAC1C,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;EAC3C,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EAC1C,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EAC1C,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;EAC/C,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;EAC/C,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EAC5C,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACxC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EAC5C,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;EACxC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EAC5C,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EAC5C,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EACzC,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;EAC/C,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;EAC/C,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EAC5C,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;EAC/C,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;EAC/C,mBAAmB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;EAC/C,mBAAmB,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;EACvD,mBAAmB,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;EACvD,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;EAC1C,mBAAmB,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;EACrD,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EACvC,mBAAmB,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;EAClD,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EACzC,mBAAmB,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;EACpD,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EACvC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;;;;;;;;;EAS1C,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;;EAE1C,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;EAC5C,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;EAC3C,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;EACpC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EACtC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;EACtC,OAAO,mBAAmB,CAAC;CAC5B,AAAC;;AAEF,IAAI,mBAAmB,GAAG,sBAAsB,EAAE,CAAC,AACnD,AAA8B,;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}