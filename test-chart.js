import axios from "axios";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { RSI, EMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

const BINANCE_API = "https://api.binance.com/api/v3/klines";
const SYMBOL = "ETHUSDT";
const INTERVAL = "1h";

// Fetch data function
async function fetchData() {
  const { data } = await axios.get(BINANCE_API, {
    params: { symbol: SYMBOL, interval: INTERVAL, limit: 50 },
  });

  return data.map((d) => ({
    time: d[0],
    open: parseFloat(d[1]),
    high: parseFloat(d[2]),
    low: parseFloat(d[3]),
    close: parseFloat(d[4]),
    volume: parseFloat(d[5]),
  }));
}

// Calculate indicators
function calculateIndicators(closes) {
  const rsi = RSI.calculate({ values: closes, period: 14 });
  const ema20 = EMA.calculate({ values: closes, period: 20 });
  const ema50 = EMA.calculate({ values: closes, period: 50 });
  return { rsi, ema20, ema50 };
}

// Enhanced chart function
async function drawChart(candles, ema20, ema50, volumes) {
  const width = 1200;
  const height = 800;
  const chartJSNodeCanvas = new ChartJSNodeCanvas({ 
    width, 
    height,
    backgroundColour: '#1a1a1a'
  });

  // Format time labels
  const timeLabels = candles.map(candle => {
    const date = new Date(candle.time);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: '2-digit',
      hour: '2-digit'
    });
  });

  const closes = candles.map(c => c.close);

  const config = {
    type: 'line',
    data: {
      labels: timeLabels,
      datasets: [
        // Price line (main trend)
        {
          label: 'Close Price',
          data: closes,
          borderColor: '#00D4FF',
          backgroundColor: 'rgba(0, 212, 255, 0.1)',
          borderWidth: 2,
          fill: false,
          pointRadius: 0,
          pointHoverRadius: 4,
          tension: 0.1
        },
        // EMA 20
        {
          label: 'EMA 20',
          data: [...Array(closes.length - ema20.length).fill(null), ...ema20],
          borderColor: '#FFA500',
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: false,
          pointRadius: 0,
          pointHoverRadius: 3,
          tension: 0.1
        },
        // EMA 50
        {
          label: 'EMA 50',
          data: [...Array(closes.length - ema50.length).fill(null), ...ema50],
          borderColor: '#FF4444',
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: false,
          pointRadius: 0,
          pointHoverRadius: 3,
          tension: 0.1
        },
        // Volume bars
        {
          label: 'Volume',
          data: volumes,
          type: 'bar',
          yAxisID: 'volume',
          backgroundColor: volumes.map((_, i) => {
            if (i === 0) return 'rgba(0, 255, 100, 0.6)';
            return candles[i].close >= candles[i-1].close ? 
              'rgba(0, 255, 100, 0.6)' : 'rgba(255, 68, 68, 0.6)';
          }),
          borderColor: 'transparent',
          borderWidth: 0,
          barPercentage: 0.8,
          categoryPercentage: 0.9
        }
      ]
    },
    options: {
      responsive: false,
      maintainAspectRatio: false,
      layout: {
        padding: {
          top: 40,
          right: 40,
          bottom: 40,
          left: 40
        }
      },
      plugins: {
        title: {
          display: true,
          text: `${SYMBOL} ${INTERVAL.toUpperCase()} Binance`,
          color: '#FFFFFF',
          font: {
            size: 20,
            weight: 'bold',
            family: 'Arial'
          },
          padding: {
            top: 10,
            bottom: 20
          }
        },
        legend: {
          display: true,
          position: 'bottom',
          labels: {
            color: '#FFFFFF',
            font: {
              size: 12,
              family: 'Arial'
            },
            padding: 20,
            usePointStyle: true,
            pointStyle: 'line'
          }
        }
      },
      scales: {
        x: {
          display: true,
          grid: {
            display: true,
            color: 'rgba(255, 255, 255, 0.1)',
            lineWidth: 1
          },
          ticks: {
            color: '#CCCCCC',
            font: {
              size: 10,
              family: 'Arial'
            },
            maxTicksLimit: 10,
            maxRotation: 45
          },
          border: {
            color: 'rgba(255, 255, 255, 0.2)'
          }
        },
        y: {
          type: 'linear',
          position: 'left',
          display: true,
          grid: {
            display: true,
            color: 'rgba(255, 255, 255, 0.1)',
            lineWidth: 1
          },
          ticks: {
            color: '#CCCCCC',
            font: {
              size: 11,
              family: 'Arial'
            },
            callback: function(value) {
              return '$' + value.toLocaleString();
            }
          },
          border: {
            color: 'rgba(255, 255, 255, 0.2)'
          }
        },
        volume: {
          type: 'linear',
          position: 'right',
          display: true,
          max: Math.max(...volumes) * 4,
          grid: {
            display: false
          },
          ticks: {
            display: false
          },
          border: {
            display: false
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      }
    }
  };

  const buffer = await chartJSNodeCanvas.renderToBuffer(config);
  fs.writeFileSync("professional-chart.png", buffer);
  return "professional-chart.png";
}

// Test the chart
async function testChart() {
  console.log("🎨 Testing professional chart generation...");
  
  try {
    const candles = await fetchData();
    const closes = candles.map(c => c.close);
    const volumes = candles.map(c => c.volume);
    
    const { rsi, ema20, ema50 } = calculateIndicators(closes);
    
    const chartPath = await drawChart(candles, ema20, ema50, volumes);
    
    console.log("✅ Professional chart generated successfully:", chartPath);
    console.log("📊 Chart features:");
    console.log("  - Dark professional theme");
    console.log("  - Enhanced color scheme (cyan price, orange EMA20, red EMA50)");
    console.log("  - Volume bars with green/red coloring");
    console.log("  - Professional grid lines and styling");
    console.log("  - Proper time labels and formatting");
    console.log("  - Title and legend");
    console.log("  - 1200x800 resolution for better quality");
    
  } catch (error) {
    console.log("❌ Chart generation failed:", error.message);
  }
}

testChart();
