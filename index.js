import axios from "axios";
import { Telegraf } from "telegraf";
import OpenAI from "openai";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { RSI, EMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

// === Config ===
const BINANCE_API = "https://api.binance.com/api/v3/klines";
const SYMBOL = "ETHUSDT";
const INTERVAL = "1h"; // timeframe
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const chatId = process.env.TELEGRAM_GROUP_ID;

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// === Fetch chart data from Binance ===
async function fetchData() {
  const { data } = await axios.get(BINANCE_API, {
    params: { symbol: SYMBOL, interval: INTERVAL, limit: 100 },
  });

  return data.map((d) => ({
    time: d[0],
    open: parseFloat(d[1]),
    high: parseFloat(d[2]),
    low: parseFloat(d[3]),
    close: parseFloat(d[4]),
    volume: parseFloat(d[5]),
  }));
}

// === Calculate Indicators ===
function calculateIndicators(closes) {
  const rsi = RSI.calculate({ values: closes, period: 14 });
  const ema20 = EMA.calculate({ values: closes, period: 20 });
  const ema50 = EMA.calculate({ values: closes, period: 50 });
  return { rsi, ema20, ema50 };
}

// === Draw chart ===
async function drawChart(closes, ema20, ema50, volumes) {
  const width = 800;
  const height = 600;
  const chartJSNodeCanvas = new ChartJSNodeCanvas({ width, height });

  const config = {
    type: "line",
    data: {
      labels: closes.map((_, i) => i),
      datasets: [
        {
          label: "Close Price",
          data: closes,
          borderColor: "blue",
          fill: false,
        },
        {
          label: "EMA 20",
          data: [...Array(closes.length - ema20.length).fill(null), ...ema20],
          borderColor: "orange",
          fill: false,
        },
        {
          label: "EMA 50",
          data: [...Array(closes.length - ema50.length).fill(null), ...ema50],
          borderColor: "red",
          fill: false,
        },
        {
          label: "Volume",
          data: volumes,
          type: "bar",
          yAxisID: "y1",
          backgroundColor: "rgba(0, 255, 0, 0.3)",
        },
      ],
    },
    options: {
      responsive: false,
      scales: {
        y: { type: "linear", position: "left" },
        y1: { type: "linear", position: "right", grid: { drawOnChartArea: false } },
      },
    },
  };

  const buffer = await chartJSNodeCanvas.renderToBuffer(config);
  fs.writeFileSync("chart.png", buffer);
  return "chart.png";
}

// === Analyze with GPT ===
async function analyzeWithGPT(closes, rsi, ema20, ema50) {
  const prompt = `
Bạn là 1 bot trade chuyên nghiệp.
Dữ liệu ETH/USDT timeframe ${INTERVAL}:

- Giá gần nhất: ${closes[closes.length - 1]}
- RSI(14): ${rsi[rsi.length - 1]}
- EMA20: ${ema20[ema20.length - 1]}
- EMA50: ${ema50[ema50.length - 1]}

Hãy phân tích nhanh xu hướng (bullish/bearish),
có nên long/short, và lý do.`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [{ role: "user", content: prompt }],
  });

  return res.choices[0].message.content;
}

// === Main Task ===
async function runBot() {
  const candles = await fetchData();
  const closes = candles.map((c) => c.close);
  const volumes = candles.map((c) => c.volume);

  const { rsi, ema20, ema50 } = calculateIndicators(closes, volumes);

  const chartPath = await drawChart(closes, ema20, ema50, volumes);
  const analysis = await analyzeWithGPT(closes, rsi, ema20, ema50);

  await bot.telegram.sendMessage(chatId, `📊 Phân tích ETH/USDT:\n${analysis}`);
  await bot.telegram.sendPhoto(chatId, { source: chartPath });
}

// === Run every hour ===
setInterval(runBot, 60 * 60 * 1000);
runBot();
