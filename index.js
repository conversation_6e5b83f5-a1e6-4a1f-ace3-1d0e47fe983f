import axios from "axios";
import { Telegraf } from "telegraf";
import OpenAI from "openai";
import { ChartJSNodeCanvas } from "chartjs-node-canvas";
import { RSI, EMA } from "technicalindicators";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

// === Config ===
const BINANCE_API = "https://api.binance.com/api/v3/klines";
const SYMBOL = "ETHUSDT";
const INTERVAL = "1h"; // timeframe
const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN);
const chatId = process.env.TELEGRAM_GROUP_ID;

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// === Fetch chart data from Binance ===
async function fetchData() {
  const { data } = await axios.get(BINANCE_API, {
    params: { symbol: SYMBOL, interval: INTERVAL, limit: 100 },
  });

  return data.map((d) => ({
    time: d[0],
    open: parseFloat(d[1]),
    high: parseFloat(d[2]),
    low: parseFloat(d[3]),
    close: parseFloat(d[4]),
    volume: parseFloat(d[5]),
  }));
}

// === Calculate Indicators ===
function calculateIndicators(closes) {
  const rsi = RSI.calculate({ values: closes, period: 14 });
  const ema20 = EMA.calculate({ values: closes, period: 20 });
  const ema50 = EMA.calculate({ values: closes, period: 50 });
  return { rsi, ema20, ema50 };
}

// === Draw chart ===
async function drawChart(candles, ema20, ema50, volumes) {
  const width = 1200;
  const height = 800;
  const chartJSNodeCanvas = new ChartJSNodeCanvas({
    width,
    height,
    backgroundColour: '#1a1a1a'
  });

  // Format time labels
  const timeLabels = candles.map(candle => {
    const date = new Date(candle.time);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: '2-digit',
      hour: '2-digit'
    });
  });



  const closes = candles.map(c => c.close);

  const config = {
    type: 'line',
    data: {
      labels: timeLabels,
      datasets: [
        // Price line (main trend)
        {
          label: 'Close Price',
          data: closes,
          borderColor: '#00D4FF',
          backgroundColor: 'rgba(0, 212, 255, 0.1)',
          borderWidth: 2,
          fill: false,
          pointRadius: 0,
          pointHoverRadius: 4,
          tension: 0.1
        },
        // EMA 20
        {
          label: 'EMA 20',
          data: [...Array(closes.length - ema20.length).fill(null), ...ema20],
          borderColor: '#FFA500',
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: false,
          pointRadius: 0,
          pointHoverRadius: 3,
          tension: 0.1
        },
        // EMA 50
        {
          label: 'EMA 50',
          data: [...Array(closes.length - ema50.length).fill(null), ...ema50],
          borderColor: '#FF4444',
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: false,
          pointRadius: 0,
          pointHoverRadius: 3,
          tension: 0.1
        },
        // Volume bars
        {
          label: 'Volume',
          data: volumes,
          type: 'bar',
          yAxisID: 'volume',
          backgroundColor: volumes.map((_, i) => {
            if (i === 0) return 'rgba(0, 255, 100, 0.6)';
            return candles[i].close >= candles[i-1].close ?
              'rgba(0, 255, 100, 0.6)' : 'rgba(255, 68, 68, 0.6)';
          }),
          borderColor: 'transparent',
          borderWidth: 0,
          barPercentage: 0.8,
          categoryPercentage: 0.9
        }
      ]
    },
    options: {
      responsive: false,
      maintainAspectRatio: false,
      layout: {
        padding: {
          top: 40,
          right: 40,
          bottom: 40,
          left: 40
        }
      },
      plugins: {
        title: {
          display: true,
          text: `${SYMBOL} ${INTERVAL.toUpperCase()} Binance`,
          color: '#FFFFFF',
          font: {
            size: 20,
            weight: 'bold',
            family: 'Arial'
          },
          padding: {
            top: 10,
            bottom: 20
          }
        },
        legend: {
          display: true,
          position: 'bottom',
          labels: {
            color: '#FFFFFF',
            font: {
              size: 12,
              family: 'Arial'
            },
            padding: 20,
            usePointStyle: true,
            pointStyle: 'line'
          }
        }
      },
      scales: {
        x: {
          display: true,
          grid: {
            display: true,
            color: 'rgba(255, 255, 255, 0.1)',
            lineWidth: 1
          },
          ticks: {
            color: '#CCCCCC',
            font: {
              size: 10,
              family: 'Arial'
            },
            maxTicksLimit: 10,
            maxRotation: 45
          },
          border: {
            color: 'rgba(255, 255, 255, 0.2)'
          }
        },
        y: {
          type: 'linear',
          position: 'left',
          display: true,
          grid: {
            display: true,
            color: 'rgba(255, 255, 255, 0.1)',
            lineWidth: 1
          },
          ticks: {
            color: '#CCCCCC',
            font: {
              size: 11,
              family: 'Arial'
            },
            callback: function(value) {
              return '$' + value.toLocaleString();
            }
          },
          border: {
            color: 'rgba(255, 255, 255, 0.2)'
          }
        },
        volume: {
          type: 'linear',
          position: 'right',
          display: true,
          max: Math.max(...volumes) * 4, // Make volume bars smaller
          grid: {
            display: false
          },
          ticks: {
            display: false
          },
          border: {
            display: false
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      }
    }
  };

  const buffer = await chartJSNodeCanvas.renderToBuffer(config);
  fs.writeFileSync("chart.png", buffer);
  return "chart.png";
}

// === Analyze with GPT ===
async function analyzeWithGPT(closes, rsi, ema20, ema50) {
  const prompt = `
Bạn là 1 bot trade chuyên nghiệp, chuyên đánh lệnh swing.
Dữ liệu ETH/USDT timeframe ${INTERVAL}:

- Giá gần nhất: ${closes[closes.length - 1]}
- RSI(14): ${rsi[rsi.length - 1]}
- EMA20: ${ema20[ema20.length - 1]}
- EMA50: ${ema50[ema50.length - 1]}

Hãy phân tích NGẮN GỌN (tối đa 800 ký tự) xu hướng (bullish/bearish),
có nên long/short, và lý do và hãy cho entry để vào lệnh + TP/SL nhất định.`;

  const res = await openai.chat.completions.create({
    model: "gpt-4o-mini",
    messages: [{ role: "user", content: prompt }],
  });

  return res.choices[0].message.content;
}

// === Main Task ===
async function runBot() {
  const candles = await fetchData();
  const closes = candles.map((c) => c.close);
  const volumes = candles.map((c) => c.volume);

  const { rsi, ema20, ema50 } = calculateIndicators(closes);

  const chartPath = await drawChart(closes, ema20, ema50, volumes);
  const analysis = await analyzeWithGPT(closes, rsi, ema20, ema50);

  // Send photo with analysis as caption in single message
  await bot.telegram.sendPhoto(chatId, { source: chartPath }, {
    caption: `📊 Phân tích ETH/USDT (${INTERVAL}):\n\n${analysis}`
  });
}

// === Run every hour ===
setInterval(runBot, 60 * 60 * 1000);
runBot();
